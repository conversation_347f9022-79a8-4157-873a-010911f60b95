#ifndef __TASK_H__
#define __TASK_H__

#include "Delay.h"
#include "Uart.h"
#include "Oled_Hardware.h"
#include "Systick.h"
#include "stdio.h"
#include "wit.h"
#include "Motor.h"
#include "Key.h"
typedef struct _Task_Struct
{
	uint8_t Runing_Flag;		// �������б�־λ 0�ȴ� 1����
	uint16_t Time_Init_Num;		// ��ʱ����ʱ��ֵ
	uint16_t Time_Cnt_Down;		// ��ʱ����������
	void (*TaskHander) (void);	// ����ִ�к���ָ��
}Task_Struct;
void Task_Init(void);
void Task_Marks(void);
void Task_Proc(void);

#endif