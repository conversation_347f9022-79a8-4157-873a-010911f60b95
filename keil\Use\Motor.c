#include "ti_msp_dl_config.h"
#include "Motor.h"
#include "KeyApp.h"

void Motor_Proc(void)
{
    // 执行按键应用处理(包含模式切换和寻迹控制)
    KeyApp_Process();
}

// 设置电机速度函数 - 基于DriverLib库的正确实现
static void setMotorSpeed(GPTIMER_Regs *timer, uint32_t speed_hz, DL_TIMER_CC_INDEX ccIndex)
{
    if (speed_hz == 0) {
        DL_TimerA_stopCounter(timer); // 停止定时器
        return;
    }

    // 计算周期值：考虑32分频和32MHz系统时钟
    // 实际时钟频率 = 32MHz / 32 = 1MHz
    uint32_t period = PWM_MOTOR1_INST_CLK_FREQ / speed_hz; // 1MHz / speed_hz

    // 停止定时器以安全修改参数
    DL_TimerA_stopCounter(timer);

    // 设置新的周期值
    DL_TimerA_setLoadValue(timer, period);

    // 设置比较值(50%占空比)
    DL_TimerA_setCaptureCompareValue(timer, period / 2, ccIndex);

    // 重新启动定时器
    DL_TimerA_startCounter(timer);
}

// 电机1控制函数
void Motor1_SetSpeed(uint32_t speed_hz)
{
    setMotorSpeed(PWM_MOTOR1_INST, speed_hz, GPIO_PWM_MOTOR1_C2_IDX);
}

// 电机2控制函数
void Motor2_SetSpeed(uint32_t speed_hz)
{
    setMotorSpeed(PWM_MOTOR2_INST, speed_hz, GPIO_PWM_MOTOR2_C1_IDX);
}

// 差速转向控制函数
void Motor_DifferentialControl(uint32_t base_speed, int16_t turn_value)
{
    uint32_t left_speed, right_speed;

    // 计算左右轮速度，turn_value为转向偏差值
    // turn_value > 0: 右转，减少右轮速度
    // turn_value < 0: 左转，减少左轮速度
    // turn_value = 0: 直行，两轮同速

    if (turn_value > 0) {
        // 右转：左轮保持基础速度，右轮减速
        left_speed = base_speed;
        if (turn_value >= base_speed) {
            right_speed = 0; // 最大转向时右轮停止
        } else {
            right_speed = base_speed - turn_value;
        }
    } else if (turn_value < 0) {
        // 左转：右轮保持基础速度，左轮减速
        right_speed = base_speed;
        if ((-turn_value) >= base_speed) {
            left_speed = 0; // 最大转向时左轮停止
        } else {
            left_speed = base_speed + turn_value; // turn_value为负数
        }
    } else {
        // 直行：两轮同速
        left_speed = base_speed;
        right_speed = base_speed;
    }

    // 设置电机速度 (假设Motor1为左轮，Motor2为右轮)
    Motor1_SetSpeed(left_speed);
    Motor2_SetSpeed(right_speed);
}

// 简化的方向控制函数
void Motor_Move(uint32_t speed, int8_t direction)
{
    switch (direction) {
        case 0: // 直行
            Motor1_SetSpeed(speed);
            Motor2_SetSpeed(speed);
            break;
        case 1: // 右转
            Motor1_SetSpeed(speed);
            Motor2_SetSpeed(speed / 2); // 右轮减半速度
            break;
        case -1: // 左转
            Motor1_SetSpeed(speed / 2); // 左轮减半速度
            Motor2_SetSpeed(speed);
            break;
        case 2: // 急右转
            Motor1_SetSpeed(speed);
            Motor2_SetSpeed(0); // 右轮停止
            break;
        case -2: // 急左转
            Motor1_SetSpeed(0); // 左轮停止
            Motor2_SetSpeed(speed);
            break;
        default: // 停止
            Motor1_SetSpeed(0);
            Motor2_SetSpeed(0);
            break;
    }
}

// 停止所有电机
void Motor_Stop(void)
{
    Motor1_SetSpeed(0);
    Motor2_SetSpeed(0);
}

// 读取7路灰度传感器状态
uint8_t Read_GraySensors(void)
{
    uint8_t sensor_data = 0;

    // 读取7路传感器，从左到右对应bit0-bit6
    if (DL_GPIO_readPins(GPIO_GRAY_PIN_1_PORT, GPIO_GRAY_PIN_1_PIN)) sensor_data |= 0x01; // 最左侧
    if (DL_GPIO_readPins(GPIO_GRAY_PIN_2_PORT, GPIO_GRAY_PIN_2_PIN)) sensor_data |= 0x02;
    if (DL_GPIO_readPins(GPIO_GRAY_PIN_3_PORT, GPIO_GRAY_PIN_3_PIN)) sensor_data |= 0x04;
    if (DL_GPIO_readPins(GPIO_GRAY_PIN_4_PORT, GPIO_GRAY_PIN_4_PIN)) sensor_data |= 0x08; // 中间
    if (DL_GPIO_readPins(GPIO_GRAY_PIN_5_PORT, GPIO_GRAY_PIN_5_PIN)) sensor_data |= 0x10;
    if (DL_GPIO_readPins(GPIO_GRAY_PIN_6_PORT, GPIO_GRAY_PIN_6_PIN)) sensor_data |= 0x20;
    if (DL_GPIO_readPins(GPIO_GRAY_PIN_7_PORT, GPIO_GRAY_PIN_7_PIN)) sensor_data |= 0x40; // 最右侧

    return sensor_data;
}

// 计算线位置偏差 (-3到+3，0为中心)
int8_t Calculate_LinePosition(uint8_t sensor_data)
{
    // 根据传感器状态判断线的位置
    switch (sensor_data) {
        // 中心位置 - 直行
        case 0b0001000: return 0;  // 只有中间传感器检测到线
        case 0b0011000: return 0;  // 中间两个传感器
        case 0b0011100: return 0;  // 中间三个传感器

        // 轻微左偏
        case 0b0000100: return -1; // 中心偏左
        case 0b0001100: return -1; // 中心和左侧
        case 0b0000110: return -1;

        // 明显左偏
        case 0b0000010: return -2; // 左侧第二个
        case 0b0000011: return -2;

        // 严重左偏
        case 0b0000001: return -3; // 最左侧

        // 轻微右偏
        case 0b0010000: return 1;  // 中心偏右
        case 0b0110000: return 1;  // 中心和右侧
        case 0b0111000: return 1;  // 中心右侧三个

        // 明显右偏
        case 0b0100000: return 2;  // 右侧第二个
        case 0b1100000: return 2;

        // 严重右偏
        case 0b1000000: return 3;  // 最右侧

        // 丢线情况
        case 0b0000000: return 0;  // 全部没检测到，保持直行
        case 0b1111111: return 0;  // 全部检测到，可能在交叉路口

        default: return 0; // 其他情况保持直行
    }
}

// 寻迹巡线主函数
void Motor_LineFollowing(void)
{
    static uint32_t base_speed = 800; // 基础速度800Hz
    uint8_t sensor_data;
    int8_t line_position;

    // 读取传感器数据
    sensor_data = Read_GraySensors();

    // 计算线位置
    line_position = Calculate_LinePosition(sensor_data);

    // 根据线位置进行差速控制
    switch (line_position) {
        case 0:  // 直行
            Motor1_SetSpeed(base_speed);
            Motor2_SetSpeed(base_speed);
            break;

        case -1: // 轻微左偏，右转修正
            Motor1_SetSpeed(base_speed);
            Motor2_SetSpeed(base_speed * 3 / 4); // 右轮75%速度
            break;

        case 1:  // 轻微右偏，左转修正
            Motor1_SetSpeed(base_speed * 3 / 4); // 左轮75%速度
            Motor2_SetSpeed(base_speed);
            break;

        case -2: // 明显左偏，加大右转
            Motor1_SetSpeed(base_speed);
            Motor2_SetSpeed(base_speed / 2); // 右轮50%速度
            break;

        case 2:  // 明显右偏，加大左转
            Motor1_SetSpeed(base_speed / 2); // 左轮50%速度
            Motor2_SetSpeed(base_speed);
            break;

        case -3: // 严重左偏，急转
            Motor1_SetSpeed(base_speed);
            Motor2_SetSpeed(0); // 右轮停止
            break;

        case 3:  // 严重右偏，急转
            Motor1_SetSpeed(0); // 左轮停止
            Motor2_SetSpeed(base_speed);
            break;

        default:
            Motor1_SetSpeed(base_speed);
            Motor2_SetSpeed(base_speed);
            break;
    }
}