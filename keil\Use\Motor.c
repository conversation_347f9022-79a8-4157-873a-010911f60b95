#include "ti_msp_dl_config.h"
#include "Motor.h"

void Motor_Proc(void)
{

}

// 设置电机速度函数 - 基于DriverLib库的正确实现
static void setMotorSpeed(GPTIMER_Regs *timer, uint32_t speed_hz, DL_TIMER_CC_INDEX ccIndex)
{
    if (speed_hz == 0) {
        DL_TimerA_stopCounter(timer); // 停止定时器
        return;
    }

    // 计算周期值：考虑32分频和32MHz系统时钟
    // 实际时钟频率 = 32MHz / 32 = 1MHz
    uint32_t period = PWM_MOTOR1_INST_CLK_FREQ / speed_hz; // 1MHz / speed_hz

    // 停止定时器以安全修改参数
    DL_TimerA_stopCounter(timer);

    // 设置新的周期值
    DL_TimerA_setLoadValue(timer, period);

    // 设置比较值(50%占空比)
    DL_TimerA_setCaptureCompareValue(timer, period / 2, ccIndex);

    // 重新启动定时器
    DL_TimerA_startCounter(timer);
}

// 电机1控制函数
void Motor1_SetSpeed(uint32_t speed_hz)
{
    setMotorSpeed(PWM_MOTOR1_INST, speed_hz, GPIO_PWM_MOTOR1_C2_IDX);
}

// 电机2控制函数
void Motor2_SetSpeed(uint32_t speed_hz)
{
    setMotorSpeed(PWM_MOTOR2_INST, speed_hz, GPIO_PWM_MOTOR2_C1_IDX);
}