#include "ti_msp_dl_config.h"
#include "Motor.h"

void Motor_Proc(void)
{

}

// 设置电机速度函数 - 基于DriverLib库的正确实现
static void setMotorSpeed(GPTIMER_Regs *timer, uint32_t speed_hz, DL_TIMER_CC_INDEX ccIndex)
{
    if (speed_hz == 0) {
        DL_TimerA_stopCounter(timer); // 停止定时器
        return;
    }

    // 计算周期值：考虑32分频和32MHz系统时钟
    // 实际时钟频率 = 32MHz / 32 = 1MHz
    uint32_t period = PWM_MOTOR1_INST_CLK_FREQ / speed_hz; // 1MHz / speed_hz

    // 停止定时器以安全修改参数
    DL_TimerA_stopCounter(timer);

    // 设置新的周期值
    DL_TimerA_setLoadValue(timer, period);

    // 设置比较值(50%占空比)
    DL_TimerA_setCaptureCompareValue(timer, period / 2, ccIndex);

    // 重新启动定时器
    DL_TimerA_startCounter(timer);
}

// 电机1控制函数
void Motor1_SetSpeed(uint32_t speed_hz)
{
    setMotorSpeed(PWM_MOTOR1_INST, speed_hz, GPIO_PWM_MOTOR1_C2_IDX);
}

// 电机2控制函数
void Motor2_SetSpeed(uint32_t speed_hz)
{
    setMotorSpeed(PWM_MOTOR2_INST, speed_hz, GPIO_PWM_MOTOR2_C1_IDX);
}

// 差速转向控制函数
void Motor_DifferentialControl(uint32_t base_speed, int16_t turn_value)
{
    uint32_t left_speed, right_speed;

    // 计算左右轮速度，turn_value为转向偏差值
    // turn_value > 0: 右转，减少右轮速度
    // turn_value < 0: 左转，减少左轮速度
    // turn_value = 0: 直行，两轮同速

    if (turn_value > 0) {
        // 右转：左轮保持基础速度，右轮减速
        left_speed = base_speed;
        if (turn_value >= base_speed) {
            right_speed = 0; // 最大转向时右轮停止
        } else {
            right_speed = base_speed - turn_value;
        }
    } else if (turn_value < 0) {
        // 左转：右轮保持基础速度，左轮减速
        right_speed = base_speed;
        if ((-turn_value) >= base_speed) {
            left_speed = 0; // 最大转向时左轮停止
        } else {
            left_speed = base_speed + turn_value; // turn_value为负数
        }
    } else {
        // 直行：两轮同速
        left_speed = base_speed;
        right_speed = base_speed;
    }

    // 设置电机速度 (假设Motor1为左轮，Motor2为右轮)
    Motor1_SetSpeed(left_speed);
    Motor2_SetSpeed(right_speed);
}

// 简化的方向控制函数
void Motor_Move(uint32_t speed, int8_t direction)
{
    switch (direction) {
        case 0: // 直行
            Motor1_SetSpeed(speed);
            Motor2_SetSpeed(speed);
            break;
        case 1: // 右转
            Motor1_SetSpeed(speed);
            Motor2_SetSpeed(speed / 2); // 右轮减半速度
            break;
        case -1: // 左转
            Motor1_SetSpeed(speed / 2); // 左轮减半速度
            Motor2_SetSpeed(speed);
            break;
        case 2: // 急右转
            Motor1_SetSpeed(speed);
            Motor2_SetSpeed(0); // 右轮停止
            break;
        case -2: // 急左转
            Motor1_SetSpeed(0); // 左轮停止
            Motor2_SetSpeed(speed);
            break;
        default: // 停止
            Motor1_SetSpeed(0);
            Motor2_SetSpeed(0);
            break;
    }
}

// 停止所有电机
void Motor_Stop(void)
{
    Motor1_SetSpeed(0);
    Motor2_SetSpeed(0);
}