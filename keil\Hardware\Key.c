#include "Key.h"
#include "Delay.h"

// 按键配置结构体
typedef struct {
    GPIOA_Regs *port;     // GPIO端口
    uint32_t pin;         // GPIO引脚
    uint8_t active_level; // 有效电平(0=低电平有效, 1=高电平有效)
} Key_Config_t;

// 按键状态结构体
typedef struct {
    Key_State_t current_state;    // 当前状态
    Key_State_t last_state;       // 上次状态
    uint16_t press_count;         // 按下计数器
    uint16_t release_count;       // 释放计数器
    uint8_t pressed_once_flag;    // 单次按下标志
} Key_Status_t;

// 按键配置表
static const Key_Config_t key_configs[KEY_NUM] = {
    {GPIO_KEY_PORT, GPIO_KEY_PIN_KEY3_PIN, 0}, // KEY3: PB12, 低电平有效(上拉输入)
    {GPIO_KEY_PORT, GPIO_KEY_PIN_KEY4_PIN, 0}  // KEY4: PB13, 低电平有效
};

// 按键状态数组
static Key_Status_t key_status[KEY_NUM];

// 按键事件回调函数指针
static Key_Callback_t key_callback = NULL;

// 按键扫描参数
#define KEY_DEBOUNCE_TIME    3    // 消抖时间(扫描次数)
#define KEY_LONG_PRESS_TIME  100  // 长按时间(扫描次数, 约1秒)

// 按键初始化
void Key_Init(void)
{
    // 清零按键状态
    for (int i = 0; i < KEY_NUM; i++) {
        key_status[i].current_state = KEY_RELEASED;
        key_status[i].last_state = KEY_RELEASED;
        key_status[i].press_count = 0;
        key_status[i].release_count = 0;
        key_status[i].pressed_once_flag = 0;
    }
    
    key_callback = NULL;
}

// 读取按键物理状态
static uint8_t Key_ReadPin(Key_Index_t key_index)
{
    if (key_index >= KEY_NUM) return 0;
    
    const Key_Config_t *config = &key_configs[key_index];
    uint8_t pin_state = DL_GPIO_readPins(config->port, config->pin) ? 1 : 0;
    
    // 根据有效电平判断按键是否按下
    return (pin_state == config->active_level) ? 1 : 0;
}

// 按键扫描函数(需要定时调用，建议10ms)
void Key_Scan(void)
{
    for (Key_Index_t i = 0; i < KEY_NUM; i++) {
        uint8_t key_pressed = Key_ReadPin(i);
        Key_Status_t *status = &key_status[i];
        
        if (key_pressed) {
            // 按键按下
            status->release_count = 0;
            status->press_count++;
            
            if (status->press_count >= KEY_DEBOUNCE_TIME) {
                // 消抖完成，确认按下
                if (status->current_state == KEY_RELEASED) {
                    // 刚按下
                    status->current_state = KEY_PRESSED;
                    status->pressed_once_flag = 1;
                    
                    // 触发按键按下回调
                    if (key_callback) {
                        key_callback(i, KEY_PRESSED);
                    }
                } else if (status->press_count >= KEY_LONG_PRESS_TIME) {
                    // 长按
                    if (status->current_state != KEY_HOLD) {
                        status->current_state = KEY_HOLD;
                        
                        // 触发长按回调
                        if (key_callback) {
                            key_callback(i, KEY_HOLD);
                        }
                    }
                }
            }
        } else {
            // 按键释放
            status->press_count = 0;
            status->release_count++;
            
            if (status->release_count >= KEY_DEBOUNCE_TIME) {
                // 消抖完成，确认释放
                if (status->current_state != KEY_RELEASED) {
                    status->last_state = status->current_state;
                    status->current_state = KEY_RELEASED;
                    
                    // 触发按键释放回调
                    if (key_callback) {
                        key_callback(i, KEY_RELEASED);
                    }
                }
            }
        }
    }
}

// 读取按键状态
Key_State_t Key_GetState(Key_Index_t key_index)
{
    if (key_index >= KEY_NUM) return KEY_RELEASED;
    return key_status[key_index].current_state;
}

// 检查按键是否按下
uint8_t Key_IsPressed(Key_Index_t key_index)
{
    if (key_index >= KEY_NUM) return 0;
    return (key_status[key_index].current_state != KEY_RELEASED) ? 1 : 0;
}

// 检查按键是否刚按下(单次触发)
uint8_t Key_IsPressedOnce(Key_Index_t key_index)
{
    if (key_index >= KEY_NUM) return 0;
    
    if (key_status[key_index].pressed_once_flag) {
        key_status[key_index].pressed_once_flag = 0; // 清除标志
        return 1;
    }
    return 0;
}

// 注册按键事件回调函数
void Key_RegisterCallback(Key_Callback_t callback)
{
    key_callback = callback;
}

// 清除按键状态
void Key_ClearState(Key_Index_t key_index)
{
    if (key_index >= KEY_NUM) return;
    
    key_status[key_index].current_state = KEY_RELEASED;
    key_status[key_index].last_state = KEY_RELEASED;
    key_status[key_index].press_count = 0;
    key_status[key_index].release_count = 0;
    key_status[key_index].pressed_once_flag = 0;
}
