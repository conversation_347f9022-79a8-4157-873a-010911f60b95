#ifndef __MOTOR_CONFIG_H__
#define __MOTOR_CONFIG_H__

// 17HS4401S步进电机技术参数
#define MOTOR_STEP_ANGLE        1.8     // 步距角1.8度
#define MOTOR_STEPS_PER_REV     200     // 每圈步数 (360/1.8)
#define MOTOR_RATED_VOLTAGE     12      // 额定电压12V
#define MOTOR_RATED_CURRENT     1.7     // 额定电流1.7A
#define MOTOR_PHASE_RESISTANCE  2.8     // 相电阻2.8Ω
#define MOTOR_PHASE_INDUCTANCE  3.2     // 相电感3.2mH

// DRV8825驱动器参数
#define DRV8825_SUPPLY_VOLTAGE  12      // 供电电压12V
#define DRV8825_MAX_CURRENT     2.5     // 最大电流2.5A
#define DRV8825_SENSE_RESISTOR  0.1     // 检测电阻0.1Ω
#define DRV8825_PWM_FREQ        25000   // 内部PWM频率25kHz

// 参考电压计算 (Vref = Imax × 8 × Rs)
// 对于1.7A: Vref = 1.7 × 8 × 0.1 = 1.36V
#define DRV8825_VREF_VOLTAGE    1.36    // 推荐参考电压1.36V

// 步进频率配置 (全步模式)
#define MOTOR_FREQ_MIN_HZ       50      // 最低频率50Hz (10RPM)
#define MOTOR_FREQ_LOW_HZ       200     // 低速200Hz (60RPM)
#define MOTOR_FREQ_MID_HZ       600     // 中速600Hz (180RPM) - 寻迹推荐
#define MOTOR_FREQ_HIGH_HZ      1000    // 高速1000Hz (300RPM)
#define MOTOR_FREQ_MAX_HZ       2000    // 最高频率2000Hz (600RPM)

// 寻迹应用推荐频率
#define MOTOR_TRACE_BASE_HZ     600     // 寻迹基础速度
#define MOTOR_TRACE_TURN_HZ     300     // 转弯速度
#define MOTOR_TRACE_STRAIGHT_HZ 800     // 直线速度

// 速度与RPM转换宏
#define FREQ_TO_RPM(freq)       ((freq) * 60 / MOTOR_STEPS_PER_REV)
#define RPM_TO_FREQ(rpm)        ((rpm) * MOTOR_STEPS_PER_REV / 60)

// 电机性能计算
// 600Hz = 600步/秒 = 3转/秒 = 180RPM (适合寻迹)
// 1000Hz = 1000步/秒 = 5转/秒 = 300RPM (适合直线)

#endif
