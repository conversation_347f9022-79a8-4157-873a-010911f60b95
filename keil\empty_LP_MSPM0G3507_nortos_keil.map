Component: ARM Compiler 6.16 Tool: armlink [5dfeaa00]

==============================================================================

Section Cross References

    empty.o(.text.main) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for SYSCFG_DL_init
    empty.o(.text.main) refers to wit.o(.text.WIT_Init) for WIT_Init
    empty.o(.text.main) refers to task.o(.text.Task_Init) for Task_Init
    empty.o(.text.main) refers to task.o(.text.Task_Proc) for Task_Proc
    empty.o(.ARM.exidx.text.main) refers to empty.o(.text.main) for [Anonymous Symbol]
    startup_mspm0g350x_uvision.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(.text) for Reset_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to systick.o(.text.SysTick_Handler) for SysTick_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to wit.o(.text.UART1_IRQHandler) for UART1_IRQHandler
    startup_mspm0g350x_uvision.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(.text) refers to __main.o(!!!main) for __main
    startup_mspm0g350x_uvision.o(.text) refers to startup_mspm0g350x_uvision.o(HEAP) for Heap_Mem
    startup_mspm0g350x_uvision.o(.text) refers to startup_mspm0g350x_uvision.o(STACK) for Stack_Mem
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for SYSCFG_DL_initPower
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for SYSCFG_DL_GPIO_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for SYSCFG_DL_SYSCTL_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR1_init) for SYSCFG_DL_PWM_MOTOR1_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR2_init) for SYSCFG_DL_PWM_MOTOR2_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_OLED_init) for SYSCFG_DL_I2C_OLED_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_WIT_init) for SYSCFG_DL_UART_WIT_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for SYSCFG_DL_UART_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init) for SYSCFG_DL_DMA_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for SYSCFG_DL_SYSTICK_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gPWM_MOTOR2Backup) for gPWM_MOTOR2Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gPWM_MOTOR1Backup) for gPWM_MOTOR1Backup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR1_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR1_init) refers to dl_timer.o(.text.DL_Timer_initFourCCPWMMode) for DL_Timer_initFourCCPWMMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR1_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR1_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR1_init) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for DL_Timer_setCaptCompUpdateMethod
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR1_init) refers to ti_msp_dl_config.o(.rodata.gPWM_MOTOR1ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR1_init) refers to ti_msp_dl_config.o(.rodata.gPWM_MOTOR1Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_MOTOR1_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR1_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR2_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR2_init) refers to dl_timer.o(.text.DL_Timer_initFourCCPWMMode) for DL_Timer_initFourCCPWMMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR2_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR2_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR2_init) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for DL_Timer_setCaptCompUpdateMethod
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR2_init) refers to ti_msp_dl_config.o(.rodata.gPWM_MOTOR2ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR2_init) refers to ti_msp_dl_config.o(.rodata.gPWM_MOTOR2Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_MOTOR2_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR2_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_OLED_init) refers to dl_i2c.o(.text.DL_I2C_setClockConfig) for DL_I2C_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_OLED_init) refers to ti_msp_dl_config.o(.rodata.gI2C_OLEDClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_I2C_OLED_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_OLED_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_WIT_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_WIT_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_WIT_init) refers to ti_msp_dl_config.o(.rodata.gUART_WITClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_WIT_init) refers to ti_msp_dl_config.o(.rodata.gUART_WITConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_WIT_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_WIT_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_WIT_init) for SYSCFG_DL_DMA_WIT_init
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for DL_TimerA_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_MOTOR1Backup) for gPWM_MOTOR1Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_MOTOR2Backup) for gPWM_MOTOR2Backup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for DL_TimerA_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_MOTOR1Backup) for gPWM_MOTOR1Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_MOTOR2Backup) for gPWM_MOTOR2Backup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_WIT_init) refers to dl_dma.o(.text.DL_DMA_initChannel) for DL_DMA_initChannel
    ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_WIT_init) refers to ti_msp_dl_config.o(.rodata.gDMA_WITConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_WIT_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_WIT_init) for [Anonymous Symbol]
    delay.o(.text.delay_ms) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    delay.o(.ARM.exidx.text.delay_ms) refers to delay.o(.text.delay_ms) for [Anonymous Symbol]
    delay.o(.text.delay_us) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    delay.o(.ARM.exidx.text.delay_us) refers to delay.o(.text.delay_us) for [Anonymous Symbol]
    uart.o(.ARM.exidx.text.send_char) refers to uart.o(.text.send_char) for [Anonymous Symbol]
    uart.o(.ARM.exidx.text.send_str) refers to uart.o(.text.send_str) for [Anonymous Symbol]
    oled_hardware.o(.text.oled_i2c_sda_unlock) refers to systick.o(.text.mspm0_delay_ms) for mspm0_delay_ms
    oled_hardware.o(.text.oled_i2c_sda_unlock) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_OLED_init) for SYSCFG_DL_I2C_OLED_init
    oled_hardware.o(.ARM.exidx.text.oled_i2c_sda_unlock) refers to oled_hardware.o(.text.oled_i2c_sda_unlock) for [Anonymous Symbol]
    oled_hardware.o(.text.OLED_ColorTurn) refers to oled_hardware.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled_hardware.o(.ARM.exidx.text.OLED_ColorTurn) refers to oled_hardware.o(.text.OLED_ColorTurn) for [Anonymous Symbol]
    oled_hardware.o(.text.OLED_WR_Byte) refers to systick.o(.text.mspm0_get_clock_ms) for mspm0_get_clock_ms
    oled_hardware.o(.text.OLED_WR_Byte) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for DL_I2C_fillControllerTXFIFO
    oled_hardware.o(.text.OLED_WR_Byte) refers to systick.o(.text.mspm0_delay_ms) for mspm0_delay_ms
    oled_hardware.o(.text.OLED_WR_Byte) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_OLED_init) for SYSCFG_DL_I2C_OLED_init
    oled_hardware.o(.ARM.exidx.text.OLED_WR_Byte) refers to oled_hardware.o(.text.OLED_WR_Byte) for [Anonymous Symbol]
    oled_hardware.o(.text.OLED_DisplayTurn) refers to oled_hardware.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled_hardware.o(.ARM.exidx.text.OLED_DisplayTurn) refers to oled_hardware.o(.text.OLED_DisplayTurn) for [Anonymous Symbol]
    oled_hardware.o(.text.OLED_Set_Pos) refers to oled_hardware.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled_hardware.o(.ARM.exidx.text.OLED_Set_Pos) refers to oled_hardware.o(.text.OLED_Set_Pos) for [Anonymous Symbol]
    oled_hardware.o(.text.OLED_Display_On) refers to oled_hardware.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled_hardware.o(.ARM.exidx.text.OLED_Display_On) refers to oled_hardware.o(.text.OLED_Display_On) for [Anonymous Symbol]
    oled_hardware.o(.text.OLED_Display_Off) refers to oled_hardware.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled_hardware.o(.ARM.exidx.text.OLED_Display_Off) refers to oled_hardware.o(.text.OLED_Display_Off) for [Anonymous Symbol]
    oled_hardware.o(.text.OLED_Clear) refers to oled_hardware.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled_hardware.o(.ARM.exidx.text.OLED_Clear) refers to oled_hardware.o(.text.OLED_Clear) for [Anonymous Symbol]
    oled_hardware.o(.text.OLED_ShowChar) refers to oled_hardware.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled_hardware.o(.text.OLED_ShowChar) refers to aeabi_sdivfast.o(.text) for __aeabi_uidivmod
    oled_hardware.o(.text.OLED_ShowChar) refers to oled_hardware.o(.rodata.asc2_1608) for asc2_1608
    oled_hardware.o(.text.OLED_ShowChar) refers to oled_hardware.o(.rodata.asc2_0806) for asc2_0806
    oled_hardware.o(.ARM.exidx.text.OLED_ShowChar) refers to oled_hardware.o(.text.OLED_ShowChar) for [Anonymous Symbol]
    oled_hardware.o(.ARM.exidx.text.oled_pow) refers to oled_hardware.o(.text.oled_pow) for [Anonymous Symbol]
    oled_hardware.o(.text.OLED_ShowNum) refers to oled_hardware.o(.text.OLED_ShowChar) for OLED_ShowChar
    oled_hardware.o(.text.OLED_ShowNum) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    oled_hardware.o(.text.OLED_ShowNum) refers to aeabi_sdivfast.o(.text) for __aeabi_uidivmod
    oled_hardware.o(.ARM.exidx.text.OLED_ShowNum) refers to oled_hardware.o(.text.OLED_ShowNum) for [Anonymous Symbol]
    oled_hardware.o(.text.OLED_ShowString) refers to oled_hardware.o(.text.OLED_ShowChar) for OLED_ShowChar
    oled_hardware.o(.ARM.exidx.text.OLED_ShowString) refers to oled_hardware.o(.text.OLED_ShowString) for [Anonymous Symbol]
    oled_hardware.o(.text.OLED_ShowChinese) refers to aeabi_sdivfast.o(.text) for __aeabi_uidivmod
    oled_hardware.o(.text.OLED_ShowChinese) refers to oled_hardware.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled_hardware.o(.text.OLED_ShowChinese) refers to oled_hardware.o(.rodata.Hzk) for Hzk
    oled_hardware.o(.ARM.exidx.text.OLED_ShowChinese) refers to oled_hardware.o(.text.OLED_ShowChinese) for [Anonymous Symbol]
    oled_hardware.o(.text.OLED_DrawBMP) refers to oled_hardware.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled_hardware.o(.ARM.exidx.text.OLED_DrawBMP) refers to oled_hardware.o(.text.OLED_DrawBMP) for [Anonymous Symbol]
    oled_hardware.o(.text.OLED_Init) refers to systick.o(.text.mspm0_delay_ms) for mspm0_delay_ms
    oled_hardware.o(.text.OLED_Init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_OLED_init) for SYSCFG_DL_I2C_OLED_init
    oled_hardware.o(.text.OLED_Init) refers to delay.o(.text.delay_ms) for delay_ms
    oled_hardware.o(.text.OLED_Init) refers to oled_hardware.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled_hardware.o(.text.OLED_Init) refers to oled_hardware.o(.text.OLED_Clear) for OLED_Clear
    oled_hardware.o(.ARM.exidx.text.OLED_Init) refers to oled_hardware.o(.text.OLED_Init) for [Anonymous Symbol]
    oled_hardware.o(.text.float_to_str_left_align) refers to fcmp.o(i._fls) for __aeabi_fcmplt
    oled_hardware.o(.text.float_to_str_left_align) refers to ffixi.o(.text) for __aeabi_f2iz
    oled_hardware.o(.text.float_to_str_left_align) refers to fflti.o(.text) for __aeabi_i2f
    oled_hardware.o(.text.float_to_str_left_align) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    oled_hardware.o(.text.float_to_str_left_align) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    oled_hardware.o(.text.float_to_str_left_align) refers to f2d.o(.text) for __aeabi_f2d
    oled_hardware.o(.text.float_to_str_left_align) refers to daddsub.o(.text) for __aeabi_dadd
    oled_hardware.o(.text.float_to_str_left_align) refers to dfixi.o(.text) for __aeabi_d2iz
    oled_hardware.o(.text.float_to_str_left_align) refers to fcmp.o(i._fgeq) for __aeabi_fcmpge
    oled_hardware.o(.text.float_to_str_left_align) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_idiv
    oled_hardware.o(.text.float_to_str_left_align) refers to aeabi_memset.o(.text) for __aeabi_memset
    oled_hardware.o(.ARM.exidx.text.float_to_str_left_align) refers to oled_hardware.o(.text.float_to_str_left_align) for [Anonymous Symbol]
    oled_hardware.o(.text.int_to_str_right_align) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_idiv
    oled_hardware.o(.text.int_to_str_right_align) refers to aeabi_memset.o(.text) for __aeabi_memset
    oled_hardware.o(.ARM.exidx.text.int_to_str_right_align) refers to oled_hardware.o(.text.int_to_str_right_align) for [Anonymous Symbol]
    oled_hardware.o(.text.OLED_ShowOneTime) refers to oled_hardware.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled_hardware.o(.text.OLED_ShowOneTime) refers to oled_hardware.o(.rodata.asc2_0806) for asc2_0806
    oled_hardware.o(.ARM.exidx.text.OLED_ShowOneTime) refers to oled_hardware.o(.text.OLED_ShowOneTime) for [Anonymous Symbol]
    oled_hardware.o(.text.Oled_Proc) refers to oled_hardware.o(.text.float_to_str_left_align) for float_to_str_left_align
    oled_hardware.o(.text.Oled_Proc) refers to wit.o(.bss.wit_data) for wit_data
    oled_hardware.o(.text.Oled_Proc) refers to oled_hardware.o(.bss.oled_buffer) for oled_buffer
    oled_hardware.o(.text.Oled_Proc) refers to oled_hardware.o(.text.OLED_ShowChar) for OLED_ShowChar
    oled_hardware.o(.text.Oled_Proc) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_idiv
    oled_hardware.o(.text.Oled_Proc) refers to aeabi_memset4.o(.text) for __aeabi_memset4
    oled_hardware.o(.text.Oled_Proc) refers to oled_hardware.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled_hardware.o(.text.Oled_Proc) refers to oled_hardware.o(.rodata.asc2_0806) for asc2_0806
    oled_hardware.o(.ARM.exidx.text.Oled_Proc) refers to oled_hardware.o(.text.Oled_Proc) for [Anonymous Symbol]
    wit.o(.text.WIT_Init) refers to wit.o(.bss.wit_dmaBuffer) for wit_dmaBuffer
    wit.o(.ARM.exidx.text.WIT_Init) refers to wit.o(.text.WIT_Init) for [Anonymous Symbol]
    wit.o(.text.UART1_IRQHandler) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    wit.o(.text.UART1_IRQHandler) refers to dflti.o(.text) for __aeabi_i2d
    wit.o(.text.UART1_IRQHandler) refers to ddiv.o(.text) for __aeabi_ddiv
    wit.o(.text.UART1_IRQHandler) refers to dfixi.o(.text) for __aeabi_d2iz
    wit.o(.text.UART1_IRQHandler) refers to d2f.o(.text) for __aeabi_d2f
    wit.o(.text.UART1_IRQHandler) refers to dmul.o(.text) for __aeabi_dmul
    wit.o(.text.UART1_IRQHandler) refers to dl_uart.o(.text.DL_UART_drainRXFIFO) for DL_UART_drainRXFIFO
    wit.o(.text.UART1_IRQHandler) refers to wit.o(.bss.wit_dmaBuffer) for wit_dmaBuffer
    wit.o(.text.UART1_IRQHandler) refers to wit.o(.bss.wit_data) for wit_data
    wit.o(.ARM.exidx.text.UART1_IRQHandler) refers to wit.o(.text.UART1_IRQHandler) for [Anonymous Symbol]
    motor.o(.text.Motor_Proc) refers to motor.o(.text.Motor_LineFollowing) for Motor_LineFollowing
    motor.o(.ARM.exidx.text.Motor_Proc) refers to motor.o(.text.Motor_Proc) for [Anonymous Symbol]
    motor.o(.text.Motor_LineFollowing) refers to motor.o(.text.Calculate_LinePosition) for Calculate_LinePosition
    motor.o(.text.Motor_LineFollowing) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor.o(.ARM.exidx.text.Motor_LineFollowing) refers to motor.o(.text.Motor_LineFollowing) for [Anonymous Symbol]
    motor.o(.text.Motor1_SetSpeed) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    motor.o(.text.Motor1_SetSpeed) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor.o(.ARM.exidx.text.Motor1_SetSpeed) refers to motor.o(.text.Motor1_SetSpeed) for [Anonymous Symbol]
    motor.o(.text.Motor2_SetSpeed) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    motor.o(.text.Motor2_SetSpeed) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor.o(.ARM.exidx.text.Motor2_SetSpeed) refers to motor.o(.text.Motor2_SetSpeed) for [Anonymous Symbol]
    motor.o(.text.Motor_DifferentialControl) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    motor.o(.text.Motor_DifferentialControl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor.o(.ARM.exidx.text.Motor_DifferentialControl) refers to motor.o(.text.Motor_DifferentialControl) for [Anonymous Symbol]
    motor.o(.text.Motor_Move) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    motor.o(.text.Motor_Move) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor.o(.ARM.exidx.text.Motor_Move) refers to motor.o(.text.Motor_Move) for [Anonymous Symbol]
    motor.o(.ARM.exidx.text.Motor_Stop) refers to motor.o(.text.Motor_Stop) for [Anonymous Symbol]
    motor.o(.ARM.exidx.text.Read_GraySensors) refers to motor.o(.text.Read_GraySensors) for [Anonymous Symbol]
    motor.o(.ARM.exidx.text.Calculate_LinePosition) refers to motor.o(.text.Calculate_LinePosition) for [Anonymous Symbol]
    task.o(.text.Task_Init) refers to task.o(.bss.task_num) for task_num
    task.o(.ARM.exidx.text.Task_Init) refers to task.o(.text.Task_Init) for [Anonymous Symbol]
    task.o(.text.Task_Marks) refers to task.o(.bss.task_num) for task_num
    task.o(.text.Task_Marks) refers to task.o(.data.MyTask) for [Anonymous Symbol]
    task.o(.ARM.exidx.text.Task_Marks) refers to task.o(.text.Task_Marks) for [Anonymous Symbol]
    task.o(.text.Task_Proc) refers to task.o(.bss.task_num) for task_num
    task.o(.text.Task_Proc) refers to task.o(.data.MyTask) for [Anonymous Symbol]
    task.o(.ARM.exidx.text.Task_Proc) refers to task.o(.text.Task_Proc) for [Anonymous Symbol]
    task.o(.data.MyTask) refers to motor.o(.text.Motor_Proc) for Motor_Proc
    task.o(.data.MyTask) refers to systick.o(.text.led_toggle) for led_toggle
    systick.o(.ARM.exidx.text.led_toggle) refers to systick.o(.text.led_toggle) for [Anonymous Symbol]
    systick.o(.text.mspm0_delay_ms) refers to systick.o(.bss.tick_ms) for tick_ms
    systick.o(.text.mspm0_delay_ms) refers to systick.o(.bss.start_time) for start_time
    systick.o(.ARM.exidx.text.mspm0_delay_ms) refers to systick.o(.text.mspm0_delay_ms) for [Anonymous Symbol]
    systick.o(.text.mspm0_get_clock_ms) refers to systick.o(.bss.tick_ms) for tick_ms
    systick.o(.ARM.exidx.text.mspm0_get_clock_ms) refers to systick.o(.text.mspm0_get_clock_ms) for [Anonymous Symbol]
    systick.o(.ARM.exidx.text.SysTick_Init) refers to systick.o(.text.SysTick_Init) for [Anonymous Symbol]
    systick.o(.text.SysTick_Handler) refers to task.o(.text.Task_Marks) for Task_Marks
    systick.o(.text.SysTick_Handler) refers to systick.o(.bss.tick_ms) for tick_ms
    systick.o(.ARM.exidx.text.SysTick_Handler) refers to systick.o(.text.SysTick_Handler) for [Anonymous Symbol]
    dl_common.o(.ARM.exidx.text.DL_Common_delayCycles) refers to dl_common.o(.text.DL_Common_delayCycles) for [Anonymous Symbol]
    dl_dma.o(.ARM.exidx.text.DL_DMA_initChannel) refers to dl_dma.o(.text.DL_DMA_initChannel) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig) refers to dl_i2c.o(.text.DL_I2C_setClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig) refers to dl_i2c.o(.text.DL_I2C_getClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig) refers to dl_timer.o(.text.DL_Timer_getClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Timer_initCaptureMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCaptureTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCompareMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode) refers to dl_timer.o(.text.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCompareTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompActUpdateMethod) refers to dl_timer.o(.text.DL_Timer_setCaptCompActUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompActUpdateMethod) refers to dl_timer.o(.text.DL_Timer_getCaptCompActUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut) refers to dl_timer.o(.text.DL_Timer_overrideCCPOut) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled) refers to dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration) refers to dl_timer.o(.text.DL_Timer_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration) refers to dl_timer.o(.text.DL_Timer_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initFourCCPWMMode) refers to dl_timer.o(.text.DL_Timer_initFourCCPWMMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_setFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_getFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode) refers to dl_timer.o(.text.DL_Timer_configQEIHallInputMode) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_init) refers to dl_uart.o(.text.DL_UART_init) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig) refers to dl_uart.o(.text.DL_UART_setClockConfig) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig) refers to dl_uart.o(.text.DL_UART_getClockConfig) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_configBaudRate) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate) refers to dl_uart.o(.text.DL_UART_configBaudRate) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode) refers to dl_uart.o(.text.DL_UART_configIrDAMode) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength) refers to dl_uart.o(.text.DL_UART_setIrDAPulseLength) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking) refers to dl_uart.o(.text.DL_UART_receiveDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck) refers to dl_uart.o(.text.DL_UART_receiveDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck) refers to dl_uart.o(.text.DL_UART_transmitDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO) refers to dl_uart.o(.text.DL_UART_drainRXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO) refers to dl_uart.o(.text.DL_UART_fillTXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Main_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Main_restoreConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_restoreConfiguration) for [Anonymous Symbol]
    aeabi_memset.o(.text) refers to rt_memclr.o(.text) for _memset
    aeabi_memset4.o(.text) refers to rt_memclr.o(.text) for _memset_w
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(.text) refers to ddiv.o(.constdata) for .constdata
    ddiv.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixi.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflti.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub.o(x$fpl$fadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub.o(x$fpl$fadd) refers to faddsub.o(x$fpl$fsub) for _fsub1
    faddsub.o(x$fpl$frsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub.o(x$fpl$frsb) refers to faddsub.o(x$fpl$fsub) for _fsub1
    faddsub.o(x$fpl$frsb) refers to faddsub.o(x$fpl$fadd) for _fadd1
    faddsub.o(x$fpl$fsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub.o(x$fpl$fsub) refers to faddsub.o(x$fpl$fadd) for _fadd1
    fcmp.o(i._feq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._feq) refers to feqf.o(x$fpl$feqf) for _fcmpeq
    fcmp.o(i._fgeq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fgeq) refers to fgef.o(x$fpl$fgeqf) for _fcmpge
    fcmp.o(i._fgr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fgr) refers to fgef.o(x$fpl$fgeqf) for _fcmpge
    fcmp.o(i._fleq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fleq) refers to flef.o(x$fpl$fleqf) for _fcmple
    fcmp.o(i._fls) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fls) refers to flef.o(x$fpl$fleqf) for _fcmple
    fcmp.o(i._fneq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fneq) refers to feqf.o(x$fpl$feqf) for _fcmpeq
    ffixi.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflti.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_idiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    feqf.o(x$fpl$feqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    feqf.o(x$fpl$feqf) refers to fcmpin.o(.text) for __fpl_fcmp_InfNaN
    fgef.o(x$fpl$fgeqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fgef.o(x$fpl$fgeqf) refers to fcmpin.o(.text) for __fpl_fcmp_InfNaN
    flef.o(x$fpl$fleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    flef.o(x$fpl$fleqf) refers to fcmpin.o(.text) for __fpl_fcmp_InfNaN
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to empty.o(.text.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    fcmpin.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmpin.o(.text) refers to cmpret.o(.text) for __fpl_cmpreturn
    fcmpin.o(.text) refers to fnan2.o(.text) for __fpl_fcheck_NaN2
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_mspm0g350x_uvision.o(.text) for __user_initial_stackheap
    sys_stackheap_outer.o(__vectab_stack_and_reset_area) refers to tempstk.o(.text) for __temporary_stack_top
    sys_stackheap_outer.o(__vectab_stack_and_reset_area) refers to __main.o(!!!main) for __main
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    cmpret.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnan2.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnan2.o(.text) refers to retnan.o(.text) for __fpl_return_NaN
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.text) for __ARM_argv_veneer
    retnan.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(.text) refers to cmpret.o(.text) for __fpl_cmpreturn
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_wrch_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing empty.o(.text), (0 bytes).
    Removing empty.o(.ARM.exidx.text.main), (8 bytes).
    Removing empty.o(.bss.i), (4 bytes).
    Removing empty.o(.bss.j), (4 bytes).
    Removing empty.o(.ARM.use_no_argv), (4 bytes).
    Removing ti_msp_dl_config.o(.text), (0 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_MOTOR1_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_MOTOR2_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_I2C_OLED_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_WIT_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration), (40 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration), (48 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_WIT_init), (8 bytes).
    Removing delay.o(.text), (0 bytes).
    Removing delay.o(.text.delay_ms), (22 bytes).
    Removing delay.o(.ARM.exidx.text.delay_ms), (8 bytes).
    Removing delay.o(.text.delay_us), (20 bytes).
    Removing delay.o(.ARM.exidx.text.delay_us), (8 bytes).
    Removing uart.o(.text), (0 bytes).
    Removing uart.o(.text.send_char), (20 bytes).
    Removing uart.o(.ARM.exidx.text.send_char), (8 bytes).
    Removing uart.o(.text.send_str), (40 bytes).
    Removing uart.o(.ARM.exidx.text.send_str), (8 bytes).
    Removing oled_hardware.o(.text), (0 bytes).
    Removing oled_hardware.o(.text.oled_i2c_sda_unlock), (144 bytes).
    Removing oled_hardware.o(.ARM.exidx.text.oled_i2c_sda_unlock), (8 bytes).
    Removing oled_hardware.o(.text.OLED_ColorTurn), (24 bytes).
    Removing oled_hardware.o(.ARM.exidx.text.OLED_ColorTurn), (8 bytes).
    Removing oled_hardware.o(.text.OLED_WR_Byte), (252 bytes).
    Removing oled_hardware.o(.ARM.exidx.text.OLED_WR_Byte), (8 bytes).
    Removing oled_hardware.o(.text.OLED_DisplayTurn), (38 bytes).
    Removing oled_hardware.o(.ARM.exidx.text.OLED_DisplayTurn), (8 bytes).
    Removing oled_hardware.o(.text.OLED_Set_Pos), (40 bytes).
    Removing oled_hardware.o(.ARM.exidx.text.OLED_Set_Pos), (8 bytes).
    Removing oled_hardware.o(.text.OLED_Display_On), (30 bytes).
    Removing oled_hardware.o(.ARM.exidx.text.OLED_Display_On), (8 bytes).
    Removing oled_hardware.o(.text.OLED_Display_Off), (30 bytes).
    Removing oled_hardware.o(.ARM.exidx.text.OLED_Display_Off), (8 bytes).
    Removing oled_hardware.o(.text.OLED_Clear), (340 bytes).
    Removing oled_hardware.o(.ARM.exidx.text.OLED_Clear), (8 bytes).
    Removing oled_hardware.o(.text.OLED_ShowChar), (216 bytes).
    Removing oled_hardware.o(.ARM.exidx.text.OLED_ShowChar), (8 bytes).
    Removing oled_hardware.o(.text.oled_pow), (18 bytes).
    Removing oled_hardware.o(.ARM.exidx.text.oled_pow), (8 bytes).
    Removing oled_hardware.o(.text.OLED_ShowNum), (156 bytes).
    Removing oled_hardware.o(.ARM.exidx.text.OLED_ShowNum), (8 bytes).
    Removing oled_hardware.o(.text.OLED_ShowString), (64 bytes).
    Removing oled_hardware.o(.ARM.exidx.text.OLED_ShowString), (8 bytes).
    Removing oled_hardware.o(.text.OLED_ShowChinese), (132 bytes).
    Removing oled_hardware.o(.ARM.exidx.text.OLED_ShowChinese), (8 bytes).
    Removing oled_hardware.o(.text.OLED_DrawBMP), (122 bytes).
    Removing oled_hardware.o(.ARM.exidx.text.OLED_DrawBMP), (8 bytes).
    Removing oled_hardware.o(.text.OLED_Init), (396 bytes).
    Removing oled_hardware.o(.ARM.exidx.text.OLED_Init), (8 bytes).
    Removing oled_hardware.o(.text.float_to_str_left_align), (316 bytes).
    Removing oled_hardware.o(.ARM.exidx.text.float_to_str_left_align), (8 bytes).
    Removing oled_hardware.o(.text.int_to_str_right_align), (88 bytes).
    Removing oled_hardware.o(.ARM.exidx.text.int_to_str_right_align), (8 bytes).
    Removing oled_hardware.o(.text.OLED_ShowOneTime), (772 bytes).
    Removing oled_hardware.o(.ARM.exidx.text.OLED_ShowOneTime), (8 bytes).
    Removing oled_hardware.o(.text.Oled_Proc), (1500 bytes).
    Removing oled_hardware.o(.ARM.exidx.text.Oled_Proc), (8 bytes).
    Removing oled_hardware.o(.rodata.asc2_0806), (552 bytes).
    Removing oled_hardware.o(.rodata.asc2_1608), (1520 bytes).
    Removing oled_hardware.o(.rodata.Hzk), (224 bytes).
    Removing oled_hardware.o(.bss.oled_buffer), (32 bytes).
    Removing wit.o(.text), (0 bytes).
    Removing wit.o(.ARM.exidx.text.WIT_Init), (8 bytes).
    Removing wit.o(.ARM.exidx.text.UART1_IRQHandler), (8 bytes).
    Removing motor.o(.text), (0 bytes).
    Removing motor.o(.ARM.exidx.text.Motor_Proc), (8 bytes).
    Removing motor.o(.ARM.exidx.text.Motor_LineFollowing), (8 bytes).
    Removing motor.o(.text.Motor1_SetSpeed), (68 bytes).
    Removing motor.o(.ARM.exidx.text.Motor1_SetSpeed), (8 bytes).
    Removing motor.o(.text.Motor2_SetSpeed), (68 bytes).
    Removing motor.o(.ARM.exidx.text.Motor2_SetSpeed), (8 bytes).
    Removing motor.o(.text.Motor_DifferentialControl), (164 bytes).
    Removing motor.o(.ARM.exidx.text.Motor_DifferentialControl), (8 bytes).
    Removing motor.o(.text.Motor_Move), (484 bytes).
    Removing motor.o(.ARM.exidx.text.Motor_Move), (8 bytes).
    Removing motor.o(.text.Motor_Stop), (32 bytes).
    Removing motor.o(.ARM.exidx.text.Motor_Stop), (8 bytes).
    Removing motor.o(.text.Read_GraySensors), (84 bytes).
    Removing motor.o(.ARM.exidx.text.Read_GraySensors), (8 bytes).
    Removing motor.o(.ARM.exidx.text.Calculate_LinePosition), (8 bytes).
    Removing task.o(.text), (0 bytes).
    Removing task.o(.ARM.exidx.text.Task_Init), (8 bytes).
    Removing task.o(.ARM.exidx.text.Task_Marks), (8 bytes).
    Removing task.o(.ARM.exidx.text.Task_Proc), (8 bytes).
    Removing key.o(.text), (0 bytes).
    Removing systick.o(.text), (0 bytes).
    Removing systick.o(.ARM.exidx.text.led_toggle), (8 bytes).
    Removing systick.o(.text.mspm0_delay_ms), (32 bytes).
    Removing systick.o(.ARM.exidx.text.mspm0_delay_ms), (8 bytes).
    Removing systick.o(.text.mspm0_get_clock_ms), (24 bytes).
    Removing systick.o(.ARM.exidx.text.mspm0_get_clock_ms), (8 bytes).
    Removing systick.o(.text.SysTick_Init), (56 bytes).
    Removing systick.o(.ARM.exidx.text.SysTick_Init), (8 bytes).
    Removing systick.o(.ARM.exidx.text.SysTick_Handler), (8 bytes).
    Removing systick.o(.bss.start_time), (4 bytes).
    Removing dl_common.o(.text), (0 bytes).
    Removing dl_common.o(.ARM.exidx.text.DL_Common_delayCycles), (8 bytes).
    Removing dl_dma.o(.text), (0 bytes).
    Removing dl_dma.o(.ARM.exidx.text.DL_DMA_initChannel), (8 bytes).
    Removing dl_i2c.o(.text), (0 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_getClockConfig), (26 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO), (44 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO), (40 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO), (48 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO), (40 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking), (20 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking), (20 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck), (8 bytes).
    Removing dl_timer.o(.text), (0 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getClockConfig), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initTimerMode), (240 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareCtl), (40 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureMode), (300 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInput), (26 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureTriggerMode), (124 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureCombinedMode), (232 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareMode), (168 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareTriggerMode), (112 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareValue), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareCtl), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptCompActUpdateMethod), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompActUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptCompActUpdateMethod), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompActUpdateMethod), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareAction), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareAction), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_overrideCCPOut), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInput), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter), (18 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_saveConfiguration), (236 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_restoreConfiguration), (244 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initFourCCPWMMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setFaultSourceConfig), (44 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getFaultSourceConfig), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_saveConfiguration), (264 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_restoreConfiguration), (276 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_configQEIHallInputMode), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode), (8 bytes).
    Removing dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode), (12 bytes).
    Removing dl_uart.o(.text), (0 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_init), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_getClockConfig), (16 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configBaudRate), (140 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configIrDAMode), (60 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_setIrDAPulseLength), (42 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataBlocking), (20 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataBlocking), (20 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataCheck), (28 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataCheck), (24 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_fillTXFIFO), (40 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_saveConfiguration), (88 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_restoreConfiguration), (120 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_saveConfiguration), (104 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_restoreConfiguration), (132 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration), (8 bytes).

256 unused section(s) (total 13480 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_idiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_idiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatterp.s                 0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  tempstk.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command_hlt.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast_div0.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/memcpset.c                       0x00000000   Number         0  aeabi_memset.o ABSOLUTE
    ../clib/memcpset.c                       0x00000000   Number         0  aeabi_memset4.o ABSOLUTE
    ../clib/memcpset.c                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/cfplib/cmp.c                    0x00000000   Number         0  fcmp.o ABSOLUTE
    ../fplib/cfplib/cmpret.c                 0x00000000   Number         0  cmpret.o ABSOLUTE
    ../fplib/cfplib/d2f.c                    0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/cfplib/daddsub.c                0x00000000   Number         0  daddsub.o ABSOLUTE
    ../fplib/cfplib/ddiv.c                   0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/cfplib/dmul.c                   0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/cfplib/f2d.c                    0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/cfplib/fcmpin.c                 0x00000000   Number         0  fcmpin.o ABSOLUTE
    ../fplib/cfplib/ffix.c                   0x00000000   Number         0  dfixi.o ABSOLUTE
    ../fplib/cfplib/ffix.c                   0x00000000   Number         0  ffixi.o ABSOLUTE
    ../fplib/cfplib/fflt.c                   0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/cfplib/fflt.c                   0x00000000   Number         0  fflti.o ABSOLUTE
    ../fplib/cfplib/fpinit.c                 0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/cfplib/nan2.c                   0x00000000   Number         0  fnan2.o ABSOLUTE
    ../fplib/cfplib/retnan.c                 0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/faddsub6m.s                     0x00000000   Number         0  faddsub.o ABSOLUTE
    ../fplib/feqf6m.s                        0x00000000   Number         0  feqf.o ABSOLUTE
    ../fplib/fgeqf6m.s                       0x00000000   Number         0  fgef.o ABSOLUTE
    ../fplib/fleqf6m.s                       0x00000000   Number         0  flef.o ABSOLUTE
    ../fplib/fmul6m.s                        0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    Delay.c                                  0x00000000   Number         0  delay.o ABSOLUTE
    Key.c                                    0x00000000   Number         0  key.o ABSOLUTE
    Motor.c                                  0x00000000   Number         0  motor.o ABSOLUTE
    Oled_Hardware.c                          0x00000000   Number         0  oled_hardware.o ABSOLUTE
    Systick.c                                0x00000000   Number         0  systick.o ABSOLUTE
    Uart.c                                   0x00000000   Number         0  uart.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    dl_common.c                              0x00000000   Number         0  dl_common.o ABSOLUTE
    dl_dma.c                                 0x00000000   Number         0  dl_dma.o ABSOLUTE
    dl_i2c.c                                 0x00000000   Number         0  dl_i2c.o ABSOLUTE
    dl_timer.c                               0x00000000   Number         0  dl_timer.o ABSOLUTE
    dl_uart.c                                0x00000000   Number         0  dl_uart.o ABSOLUTE
    empty.c                                  0x00000000   Number         0  empty.o ABSOLUTE
    startup_mspm0g350x_uvision.s             0x00000000   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    task.c                                   0x00000000   Number         0  task.o ABSOLUTE
    ti_msp_dl_config.c                       0x00000000   Number         0  ti_msp_dl_config.o ABSOLUTE
    wit.c                                    0x00000000   Number         0  wit.o ABSOLUTE
    RESET                                    0x00000000   Section      192  startup_mspm0g350x_uvision.o(RESET)
    !!!main                                  0x000000c0   Section        8  __main.o(!!!main)
    !!!scatter                               0x000000c8   Section       60  __scatter.o(!!!scatter)
    !!handler_copy                           0x00000108   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x00000128   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x00000144   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x00000146   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x00000148   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x0000014a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x0000014a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x0000014a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x0000014a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x0000014a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x0000014a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x0000014a   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x0000014c   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x0000014c   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x0000014c   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x00000152   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x00000152   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x00000156   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x00000156   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0000015e   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x00000160   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x00000160   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x00000164   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x0000016c   Section       48  startup_mspm0g350x_uvision.o(.text)
    .text                                    0x0000019c   Section        0  heapauxi.o(.text)
    .text                                    0x000001a4   Section        0  d2f.o(.text)
    .text                                    0x00000220   Section        0  ddiv.o(.text)
    .text                                    0x00000668   Section        0  dfixi.o(.text)
    .text                                    0x000006d4   Section        0  dflti.o(.text)
    .text                                    0x0000072c   Section        0  dmul.o(.text)
    .text                                    0x00000974   Section       62  sys_stackheap_outer.o(.text)
    .text                                    0x000009b2   Section        0  exit.o(.text)
    .text                                    0x000009c4   Section        8  libspace.o(.text)
    .text                                    0x000009cc   Section        0  sys_exit.o(.text)
    .text                                    0x000009d8   Section        2  use_no_semi.o(.text)
    .text                                    0x000009da   Section        0  indicate_semi.o(.text)
    [Anonymous Symbol]                       0x000009dc   Section        0  motor.o(.text.Calculate_LinePosition)
    [Anonymous Symbol]                       0x00000a74   Section        0  dl_common.o(.text.DL_Common_delayCycles)
    [Anonymous Symbol]                       0x00000a80   Section        0  dl_dma.o(.text.DL_DMA_initChannel)
    __arm_cp.0_0                             0x00000ac0   Number         4  dl_dma.o(.text.DL_DMA_initChannel)
    [Anonymous Symbol]                       0x00000ac4   Section        0  dl_i2c.o(.text.DL_I2C_setClockConfig)
    [Anonymous Symbol]                       0x00000aec   Section        0  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_0                            0x00000be0   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_1                            0x00000be4   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_2                            0x00000be8   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_3                            0x00000bec   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_4                            0x00000bf0   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_5                            0x00000bf4   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_6                            0x00000bf8   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    [Anonymous Symbol]                       0x00000bfc   Section        0  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    __arm_cp.23_0                            0x00000c14   Number         4  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    [Anonymous Symbol]                       0x00000c18   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    __arm_cp.27_0                            0x00000c2c   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    [Anonymous Symbol]                       0x00000c30   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    __arm_cp.3_0                             0x00000c3c   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    [Anonymous Symbol]                       0x00000c40   Section        0  dl_timer.o(.text.DL_Timer_setClockConfig)
    __arm_cp.0_0                             0x00000c58   Number         4  dl_timer.o(.text.DL_Timer_setClockConfig)
    [Anonymous Symbol]                       0x00000c5c   Section        0  dl_uart.o(.text.DL_UART_drainRXFIFO)
    __arm_cp.10_0                            0x00000c80   Number         4  dl_uart.o(.text.DL_UART_drainRXFIFO)
    [Anonymous Symbol]                       0x00000c84   Section        0  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_0                             0x00000cc4   Number         4  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_1                             0x00000cc8   Number         4  dl_uart.o(.text.DL_UART_init)
    [Anonymous Symbol]                       0x00000ccc   Section        0  dl_uart.o(.text.DL_UART_setClockConfig)
    [Anonymous Symbol]                       0x00000ce0   Section        0  motor.o(.text.Motor_LineFollowing)
    __arm_cp.1_0                             0x00000f04   Number         4  motor.o(.text.Motor_LineFollowing)
    __arm_cp.1_1                             0x00000f08   Number         4  motor.o(.text.Motor_LineFollowing)
    __arm_cp.1_2                             0x00000f0c   Number         4  motor.o(.text.Motor_LineFollowing)
    __arm_cp.1_3                             0x00000f10   Number         4  motor.o(.text.Motor_LineFollowing)
    __arm_cp.1_4                             0x00000f14   Number         4  motor.o(.text.Motor_LineFollowing)
    __arm_cp.1_5                             0x00000f18   Number         4  motor.o(.text.Motor_LineFollowing)
    __arm_cp.1_8                             0x00000f1c   Number         4  motor.o(.text.Motor_LineFollowing)
    __arm_cp.1_9                             0x00000f20   Number         4  motor.o(.text.Motor_LineFollowing)
    [Anonymous Symbol]                       0x00000f24   Section        0  motor.o(.text.Motor_Proc)
    [Anonymous Symbol]                       0x00000f2c   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_WIT_init)
    __arm_cp.13_0                            0x00000f48   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_WIT_init)
    __arm_cp.13_1                            0x00000f4c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_WIT_init)
    __arm_cp.13_2                            0x00000f50   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_WIT_init)
    [Anonymous Symbol]                       0x00000f54   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init)
    [Anonymous Symbol]                       0x00000f5c   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_0                             0x00000fbc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_1                             0x00000fc0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_2                             0x00000fc4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_3                             0x00000fc8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_4                             0x00000fcc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_5                             0x00000fd0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_6                             0x00000fd4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_7                             0x00000fd8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    [Anonymous Symbol]                       0x00000fdc   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_OLED_init)
    __arm_cp.6_0                             0x0000102c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_OLED_init)
    __arm_cp.6_1                             0x00001030   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_OLED_init)
    __arm_cp.6_2                             0x00001034   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_OLED_init)
    __arm_cp.6_3                             0x00001038   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_OLED_init)
    [Anonymous Symbol]                       0x0000103c   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR1_init)
    __arm_cp.4_0                             0x00001088   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR1_init)
    __arm_cp.4_1                             0x0000108c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR1_init)
    __arm_cp.4_2                             0x00001090   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR1_init)
    __arm_cp.4_3                             0x00001094   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR1_init)
    [Anonymous Symbol]                       0x00001098   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR2_init)
    __arm_cp.5_0                             0x000010e4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR2_init)
    __arm_cp.5_1                             0x000010e8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR2_init)
    __arm_cp.5_2                             0x000010ec   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR2_init)
    __arm_cp.5_3                             0x000010f0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR2_init)
    [Anonymous Symbol]                       0x000010f4   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_0                             0x0000112c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_1                             0x00001130   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    [Anonymous Symbol]                       0x00001134   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    __arm_cp.10_0                            0x00001158   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    __arm_cp.10_1                            0x0000115c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    __arm_cp.10_2                            0x00001160   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    [Anonymous Symbol]                       0x00001164   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.8_0                             0x000011b0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.8_1                             0x000011b4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.8_2                             0x000011b8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.8_3                             0x000011bc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    [Anonymous Symbol]                       0x000011c0   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_WIT_init)
    __arm_cp.7_1                             0x0000123c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_WIT_init)
    __arm_cp.7_2                             0x00001240   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_WIT_init)
    __arm_cp.7_3                             0x00001244   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_WIT_init)
    __arm_cp.7_4                             0x00001248   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_WIT_init)
    __arm_cp.7_5                             0x0000124c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_WIT_init)
    [Anonymous Symbol]                       0x00001250   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_0                             0x00001288   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_1                             0x0000128c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    [Anonymous Symbol]                       0x00001290   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_0                             0x000012cc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_1                             0x000012d0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_2                             0x000012d4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_3                             0x000012d8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_4                             0x000012dc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_5                             0x000012e0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_6                             0x000012e4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_7                             0x000012e8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_8                             0x000012ec   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    [Anonymous Symbol]                       0x000012f0   Section        0  systick.o(.text.SysTick_Handler)
    __arm_cp.4_0                             0x00001300   Number         4  systick.o(.text.SysTick_Handler)
    [Anonymous Symbol]                       0x00001304   Section        0  task.o(.text.Task_Init)
    [Anonymous Symbol]                       0x0000130c   Section        0  task.o(.text.Task_Marks)
    [Anonymous Symbol]                       0x00001338   Section        0  task.o(.text.Task_Proc)
    __arm_cp.2_0                             0x00001364   Number         4  task.o(.text.Task_Proc)
    __arm_cp.2_1                             0x00001368   Number         4  task.o(.text.Task_Proc)
    [Anonymous Symbol]                       0x0000136c   Section        0  wit.o(.text.UART1_IRQHandler)
    __arm_cp.1_1                             0x0000158c   Number         4  wit.o(.text.UART1_IRQHandler)
    __arm_cp.1_3                             0x00001590   Number         4  wit.o(.text.UART1_IRQHandler)
    __arm_cp.1_4                             0x00001594   Number         4  wit.o(.text.UART1_IRQHandler)
    __arm_cp.1_5                             0x00001598   Number         4  wit.o(.text.UART1_IRQHandler)
    __arm_cp.1_6                             0x0000159c   Number         4  wit.o(.text.UART1_IRQHandler)
    __arm_cp.1_7                             0x000015a0   Number         4  wit.o(.text.UART1_IRQHandler)
    __arm_cp.1_8                             0x000015a4   Number         4  wit.o(.text.UART1_IRQHandler)
    __arm_cp.1_9                             0x000015a8   Number         4  wit.o(.text.UART1_IRQHandler)
    [Anonymous Symbol]                       0x000015ac   Section        0  wit.o(.text.WIT_Init)
    __arm_cp.0_0                             0x000015cc   Number         4  wit.o(.text.WIT_Init)
    __arm_cp.0_1                             0x000015d0   Number         4  wit.o(.text.WIT_Init)
    __arm_cp.0_2                             0x000015d4   Number         4  wit.o(.text.WIT_Init)
    __arm_cp.0_3                             0x000015d8   Number         4  wit.o(.text.WIT_Init)
    [Anonymous Symbol]                       0x000015dc   Section        0  systick.o(.text.led_toggle)
    __arm_cp.0_0                             0x000015e8   Number         4  systick.o(.text.led_toggle)
    [Anonymous Symbol]                       0x000015ec   Section        0  empty.o(.text.main)
    __arm_cp.0_0                             0x00001608   Number         4  empty.o(.text.main)
    .text_divfast                            0x0000160c   Section      502  aeabi_sdivfast.o(.text_divfast)
    ddiv_reciptbl                            0x00001802   Data         128  ddiv.o(.constdata)
    .constdata                               0x00001802   Section      128  ddiv.o(.constdata)
    x$fpl$usenofp                            0x00001802   Section        0  usenofp.o(x$fpl$usenofp)
    gDMA_WITConfig                           0x00001884   Data          24  ti_msp_dl_config.o(.rodata.gDMA_WITConfig)
    [Anonymous Symbol]                       0x00001884   Section        0  ti_msp_dl_config.o(.rodata.gDMA_WITConfig)
    gI2C_OLEDClockConfig                     0x0000189c   Data           2  ti_msp_dl_config.o(.rodata.gI2C_OLEDClockConfig)
    [Anonymous Symbol]                       0x0000189c   Section        0  ti_msp_dl_config.o(.rodata.gI2C_OLEDClockConfig)
    gPWM_MOTOR1ClockConfig                   0x0000189e   Data           3  ti_msp_dl_config.o(.rodata.gPWM_MOTOR1ClockConfig)
    [Anonymous Symbol]                       0x0000189e   Section        0  ti_msp_dl_config.o(.rodata.gPWM_MOTOR1ClockConfig)
    gPWM_MOTOR1Config                        0x000018a4   Data           8  ti_msp_dl_config.o(.rodata.gPWM_MOTOR1Config)
    [Anonymous Symbol]                       0x000018a4   Section        0  ti_msp_dl_config.o(.rodata.gPWM_MOTOR1Config)
    gPWM_MOTOR2ClockConfig                   0x000018ac   Data           3  ti_msp_dl_config.o(.rodata.gPWM_MOTOR2ClockConfig)
    [Anonymous Symbol]                       0x000018ac   Section        0  ti_msp_dl_config.o(.rodata.gPWM_MOTOR2ClockConfig)
    gPWM_MOTOR2Config                        0x000018b0   Data           8  ti_msp_dl_config.o(.rodata.gPWM_MOTOR2Config)
    [Anonymous Symbol]                       0x000018b0   Section        0  ti_msp_dl_config.o(.rodata.gPWM_MOTOR2Config)
    gUART_0ClockConfig                       0x000018b8   Data           2  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    [Anonymous Symbol]                       0x000018b8   Section        0  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    gUART_0Config                            0x000018ba   Data          10  ti_msp_dl_config.o(.rodata.gUART_0Config)
    [Anonymous Symbol]                       0x000018ba   Section        0  ti_msp_dl_config.o(.rodata.gUART_0Config)
    gUART_WITClockConfig                     0x000018c4   Data           2  ti_msp_dl_config.o(.rodata.gUART_WITClockConfig)
    [Anonymous Symbol]                       0x000018c4   Section        0  ti_msp_dl_config.o(.rodata.gUART_WITClockConfig)
    gUART_WITConfig                          0x000018c6   Data          10  ti_msp_dl_config.o(.rodata.gUART_WITConfig)
    [Anonymous Symbol]                       0x000018c6   Section        0  ti_msp_dl_config.o(.rodata.gUART_WITConfig)
    MyTask                                   0x20200000   Data          24  task.o(.data.MyTask)
    [Anonymous Symbol]                       0x20200000   Section        0  task.o(.data.MyTask)
    .bss                                     0x20200018   Section       96  libspace.o(.bss)
    Heap_Mem                                 0x20200240   Data        2048  startup_mspm0g350x_uvision.o(HEAP)
    HEAP                                     0x20200240   Section     2048  startup_mspm0g350x_uvision.o(HEAP)
    Stack_Mem                                0x20200a40   Data        1500  startup_mspm0g350x_uvision.o(STACK)
    STACK                                    0x20200a40   Section     1500  startup_mspm0g350x_uvision.o(STACK)
    __initial_sp                             0x2020101c   Data           0  startup_mspm0g350x_uvision.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv3M$S$PE$A:L22$X:L11$S22$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __Vectors                                0x00000000   Data           4  startup_mspm0g350x_uvision.o(RESET)
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_End                            0x000000c0   Data           0  startup_mspm0g350x_uvision.o(RESET)
    __Vectors_Size                           0x000000c0   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    __main                                   0x000000c1   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x000000c9   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x000000c9   Thumb Code    52  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x000000c9   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x000000d9   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x00000109   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x00000129   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x00000145   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_1                       0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x00000149   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x0000014b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x0000014b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x0000014b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x0000014b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x0000014b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x0000014b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x0000014b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x0000014d   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x0000014d   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x0000014d   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x00000153   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x00000153   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x00000157   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x00000157   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0000015f   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x00000161   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x00000161   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x00000165   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x0000016d   Thumb Code     4  startup_mspm0g350x_uvision.o(.text)
    NMI_Handler                              0x00000171   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    HardFault_Handler                        0x00000173   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    SVC_Handler                              0x00000175   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    PendSV_Handler                           0x00000177   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    ADC0_IRQHandler                          0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    ADC1_IRQHandler                          0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    AES_IRQHandler                           0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    CANFD0_IRQHandler                        0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DAC0_IRQHandler                          0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DMA_IRQHandler                           0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    Default_Handler                          0x0000017b   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    GROUP0_IRQHandler                        0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    GROUP1_IRQHandler                        0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C0_IRQHandler                          0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C1_IRQHandler                          0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    RTC_IRQHandler                           0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI0_IRQHandler                          0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI1_IRQHandler                          0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA0_IRQHandler                         0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA1_IRQHandler                         0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG0_IRQHandler                         0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG12_IRQHandler                        0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG6_IRQHandler                         0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG7_IRQHandler                         0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG8_IRQHandler                         0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART0_IRQHandler                         0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART2_IRQHandler                         0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART3_IRQHandler                         0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    __user_initial_stackheap                 0x0000017d   Thumb Code    10  startup_mspm0g350x_uvision.o(.text)
    __use_two_region_memory                  0x0000019d   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x0000019f   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x000001a1   Thumb Code     2  heapauxi.o(.text)
    __aeabi_d2f                              0x000001a5   Thumb Code     0  d2f.o(.text)
    _d2f                                     0x000001a5   Thumb Code   120  d2f.o(.text)
    __aeabi_ddiv                             0x00000221   Thumb Code     0  ddiv.o(.text)
    _ddiv                                    0x00000221   Thumb Code  1072  ddiv.o(.text)
    _drdiv                                   0x00000651   Thumb Code    20  ddiv.o(.text)
    __aeabi_d2iz                             0x00000669   Thumb Code     0  dfixi.o(.text)
    _dfix                                    0x00000669   Thumb Code    98  dfixi.o(.text)
    __aeabi_i2d_normalise                    0x000006d5   Thumb Code    66  dflti.o(.text)
    __aeabi_i2d                              0x00000717   Thumb Code    16  dflti.o(.text)
    _dflt                                    0x00000717   Thumb Code     0  dflti.o(.text)
    __aeabi_ui2d                             0x00000727   Thumb Code     6  dflti.o(.text)
    _dfltu                                   0x00000727   Thumb Code     0  dflti.o(.text)
    __aeabi_dmul                             0x0000072d   Thumb Code     0  dmul.o(.text)
    _dmul                                    0x0000072d   Thumb Code   558  dmul.o(.text)
    __user_setup_stackheap                   0x00000975   Thumb Code    62  sys_stackheap_outer.o(.text)
    exit                                     0x000009b3   Thumb Code    16  exit.o(.text)
    __user_libspace                          0x000009c5   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x000009c5   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x000009c5   Thumb Code     0  libspace.o(.text)
    _sys_exit                                0x000009cd   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x000009d9   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x000009d9   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x000009db   Thumb Code     0  indicate_semi.o(.text)
    Calculate_LinePosition                   0x000009dd   Thumb Code   152  motor.o(.text.Calculate_LinePosition)
    DL_Common_delayCycles                    0x00000a75   Thumb Code    10  dl_common.o(.text.DL_Common_delayCycles)
    DL_DMA_initChannel                       0x00000a81   Thumb Code    68  dl_dma.o(.text.DL_DMA_initChannel)
    DL_I2C_setClockConfig                    0x00000ac5   Thumb Code    38  dl_i2c.o(.text.DL_I2C_setClockConfig)
    DL_Timer_initFourCCPWMMode               0x00000aed   Thumb Code   272  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    DL_Timer_setCaptCompUpdateMethod         0x00000bfd   Thumb Code    28  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    DL_Timer_setCaptureCompareOutCtl         0x00000c19   Thumb Code    24  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    DL_Timer_setCaptureCompareValue          0x00000c31   Thumb Code    16  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    DL_Timer_setClockConfig                  0x00000c41   Thumb Code    28  dl_timer.o(.text.DL_Timer_setClockConfig)
    DL_UART_drainRXFIFO                      0x00000c5d   Thumb Code    40  dl_uart.o(.text.DL_UART_drainRXFIFO)
    DL_UART_init                             0x00000c85   Thumb Code    72  dl_uart.o(.text.DL_UART_init)
    DL_UART_setClockConfig                   0x00000ccd   Thumb Code    18  dl_uart.o(.text.DL_UART_setClockConfig)
    Motor_LineFollowing                      0x00000ce1   Thumb Code   548  motor.o(.text.Motor_LineFollowing)
    Motor_Proc                               0x00000f25   Thumb Code     8  motor.o(.text.Motor_Proc)
    SYSCFG_DL_DMA_WIT_init                   0x00000f2d   Thumb Code    28  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_WIT_init)
    SYSCFG_DL_DMA_init                       0x00000f55   Thumb Code     8  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init)
    SYSCFG_DL_GPIO_init                      0x00000f5d   Thumb Code    96  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    SYSCFG_DL_I2C_OLED_init                  0x00000fdd   Thumb Code    80  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_OLED_init)
    SYSCFG_DL_PWM_MOTOR1_init                0x0000103d   Thumb Code    76  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR1_init)
    SYSCFG_DL_PWM_MOTOR2_init                0x00001099   Thumb Code    76  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR2_init)
    SYSCFG_DL_SYSCTL_init                    0x000010f5   Thumb Code    56  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    SYSCFG_DL_SYSTICK_init                   0x00001135   Thumb Code    36  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    SYSCFG_DL_UART_0_init                    0x00001165   Thumb Code    76  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    SYSCFG_DL_UART_WIT_init                  0x000011c1   Thumb Code   124  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_WIT_init)
    SYSCFG_DL_init                           0x00001251   Thumb Code    56  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    SYSCFG_DL_initPower                      0x00001291   Thumb Code    60  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    SysTick_Handler                          0x000012f1   Thumb Code    16  systick.o(.text.SysTick_Handler)
    Task_Init                                0x00001305   Thumb Code     8  task.o(.text.Task_Init)
    Task_Marks                               0x0000130d   Thumb Code    44  task.o(.text.Task_Marks)
    Task_Proc                                0x00001339   Thumb Code    44  task.o(.text.Task_Proc)
    UART1_IRQHandler                         0x0000136d   Thumb Code   544  wit.o(.text.UART1_IRQHandler)
    WIT_Init                                 0x000015ad   Thumb Code    32  wit.o(.text.WIT_Init)
    led_toggle                               0x000015dd   Thumb Code    12  systick.o(.text.led_toggle)
    main                                     0x000015ed   Thumb Code    28  empty.o(.text.main)
    __aeabi_uidiv                            0x0000160d   Thumb Code    68  aeabi_sdivfast.o(.text_divfast)
    __aeabi_idiv                             0x00001651   Thumb Code   434  aeabi_sdivfast.o(.text_divfast)
    __I$use$fp                               0x00001802   Number         0  usenofp.o(x$fpl$usenofp)
    Region$$Table$$Base                      0x000018d0   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x000018f0   Number         0  anon$$obj.o(Region$$Table)
    __libspace_start                         0x20200018   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20200078   Data           0  libspace.o(.bss)
    gPWM_MOTOR1Backup                        0x20200078   Data         188  ti_msp_dl_config.o(.bss.gPWM_MOTOR1Backup)
    gPWM_MOTOR2Backup                        0x20200134   Data         188  ti_msp_dl_config.o(.bss.gPWM_MOTOR2Backup)
    task_num                                 0x202001f0   Data           1  task.o(.bss.task_num)
    tick_ms                                  0x202001f4   Data           4  systick.o(.bss.tick_ms)
    wit_data                                 0x202001f8   Data          32  wit.o(.bss.wit_data)
    wit_dmaBuffer                            0x20200218   Data          33  wit.o(.bss.wit_dmaBuffer)



==============================================================================

Memory Map of the image

  Image Entry point : 0x000000c1

  Load Region LR_IROM1 (Base: 0x00000000, Size: 0x00001908, Max: 0x00020000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x00000000, Load base: 0x00000000, Size: 0x000018f0, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x00000000   0x00000000   0x000000c0   Data   RO           14    RESET               startup_mspm0g350x_uvision.o
    0x000000c0   0x000000c0   0x00000008   Code   RO          428  * !!!main             c_p.l(__main.o)
    0x000000c8   0x000000c8   0x0000003c   Code   RO          659    !!!scatter          c_p.l(__scatter.o)
    0x00000104   0x00000104   0x00000004   PAD
    0x00000108   0x00000108   0x0000001a   Code   RO          661    !!handler_copy      c_p.l(__scatter_copy.o)
    0x00000122   0x00000122   0x00000006   PAD
    0x00000128   0x00000128   0x0000001c   Code   RO          663    !!handler_zi        c_p.l(__scatter_zi.o)
    0x00000144   0x00000144   0x00000002   Code   RO          516    .ARM.Collect$$libinit$$00000000  c_p.l(libinit.o)
    0x00000146   0x00000146   0x00000000   Code   RO          534    .ARM.Collect$$libinit$$00000002  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO          536    .ARM.Collect$$libinit$$00000004  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO          539    .ARM.Collect$$libinit$$0000000A  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO          541    .ARM.Collect$$libinit$$0000000C  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO          543    .ARM.Collect$$libinit$$0000000E  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO          546    .ARM.Collect$$libinit$$00000011  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO          548    .ARM.Collect$$libinit$$00000013  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO          550    .ARM.Collect$$libinit$$00000015  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO          552    .ARM.Collect$$libinit$$00000017  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO          554    .ARM.Collect$$libinit$$00000019  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO          556    .ARM.Collect$$libinit$$0000001B  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO          558    .ARM.Collect$$libinit$$0000001D  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO          560    .ARM.Collect$$libinit$$0000001F  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO          562    .ARM.Collect$$libinit$$00000021  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO          564    .ARM.Collect$$libinit$$00000023  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO          566    .ARM.Collect$$libinit$$00000025  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO          570    .ARM.Collect$$libinit$$0000002C  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO          572    .ARM.Collect$$libinit$$0000002E  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO          574    .ARM.Collect$$libinit$$00000030  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO          576    .ARM.Collect$$libinit$$00000032  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000002   Code   RO          577    .ARM.Collect$$libinit$$00000033  c_p.l(libinit2.o)
    0x00000148   0x00000148   0x00000002   Code   RO          614    .ARM.Collect$$libshutdown$$00000000  c_p.l(libshutdown.o)
    0x0000014a   0x0000014a   0x00000000   Code   RO          642    .ARM.Collect$$libshutdown$$00000002  c_p.l(libshutdown2.o)
    0x0000014a   0x0000014a   0x00000000   Code   RO          644    .ARM.Collect$$libshutdown$$00000004  c_p.l(libshutdown2.o)
    0x0000014a   0x0000014a   0x00000000   Code   RO          647    .ARM.Collect$$libshutdown$$00000007  c_p.l(libshutdown2.o)
    0x0000014a   0x0000014a   0x00000000   Code   RO          650    .ARM.Collect$$libshutdown$$0000000A  c_p.l(libshutdown2.o)
    0x0000014a   0x0000014a   0x00000000   Code   RO          652    .ARM.Collect$$libshutdown$$0000000C  c_p.l(libshutdown2.o)
    0x0000014a   0x0000014a   0x00000000   Code   RO          655    .ARM.Collect$$libshutdown$$0000000F  c_p.l(libshutdown2.o)
    0x0000014a   0x0000014a   0x00000002   Code   RO          656    .ARM.Collect$$libshutdown$$00000010  c_p.l(libshutdown2.o)
    0x0000014c   0x0000014c   0x00000000   Code   RO          469    .ARM.Collect$$rtentry$$00000000  c_p.l(__rtentry.o)
    0x0000014c   0x0000014c   0x00000000   Code   RO          484    .ARM.Collect$$rtentry$$00000002  c_p.l(__rtentry2.o)
    0x0000014c   0x0000014c   0x00000006   Code   RO          496    .ARM.Collect$$rtentry$$00000004  c_p.l(__rtentry4.o)
    0x00000152   0x00000152   0x00000000   Code   RO          486    .ARM.Collect$$rtentry$$00000009  c_p.l(__rtentry2.o)
    0x00000152   0x00000152   0x00000004   Code   RO          487    .ARM.Collect$$rtentry$$0000000A  c_p.l(__rtentry2.o)
    0x00000156   0x00000156   0x00000000   Code   RO          489    .ARM.Collect$$rtentry$$0000000C  c_p.l(__rtentry2.o)
    0x00000156   0x00000156   0x00000008   Code   RO          490    .ARM.Collect$$rtentry$$0000000D  c_p.l(__rtentry2.o)
    0x0000015e   0x0000015e   0x00000002   Code   RO          525    .ARM.Collect$$rtexit$$00000000  c_p.l(rtexit.o)
    0x00000160   0x00000160   0x00000000   Code   RO          585    .ARM.Collect$$rtexit$$00000002  c_p.l(rtexit2.o)
    0x00000160   0x00000160   0x00000004   Code   RO          586    .ARM.Collect$$rtexit$$00000003  c_p.l(rtexit2.o)
    0x00000164   0x00000164   0x00000006   Code   RO          587    .ARM.Collect$$rtexit$$00000004  c_p.l(rtexit2.o)
    0x0000016a   0x0000016a   0x00000002   PAD
    0x0000016c   0x0000016c   0x00000030   Code   RO           15    .text               startup_mspm0g350x_uvision.o
    0x0000019c   0x0000019c   0x00000006   Code   RO          426    .text               c_p.l(heapauxi.o)
    0x000001a2   0x000001a2   0x00000002   PAD
    0x000001a4   0x000001a4   0x0000007c   Code   RO          430    .text               fz_ps.l(d2f.o)
    0x00000220   0x00000220   0x00000448   Code   RO          434    .text               fz_ps.l(ddiv.o)
    0x00000668   0x00000668   0x0000006c   Code   RO          437    .text               fz_ps.l(dfixi.o)
    0x000006d4   0x000006d4   0x00000058   Code   RO          439    .text               fz_ps.l(dflti.o)
    0x0000072c   0x0000072c   0x00000248   Code   RO          441    .text               fz_ps.l(dmul.o)
    0x00000974   0x00000974   0x0000003e   Code   RO          502    .text               c_p.l(sys_stackheap_outer.o)
    0x000009b2   0x000009b2   0x00000010   Code   RO          505    .text               c_p.l(exit.o)
    0x000009c2   0x000009c2   0x00000002   PAD
    0x000009c4   0x000009c4   0x00000008   Code   RO          521    .text               c_p.l(libspace.o)
    0x000009cc   0x000009cc   0x0000000c   Code   RO          580    .text               c_p.l(sys_exit.o)
    0x000009d8   0x000009d8   0x00000002   Code   RO          603    .text               c_p.l(use_no_semi.o)
    0x000009da   0x000009da   0x00000000   Code   RO          605    .text               c_p.l(indicate_semi.o)
    0x000009da   0x000009da   0x00000002   PAD
    0x000009dc   0x000009dc   0x00000098   Code   RO          174    .text.Calculate_LinePosition  motor.o
    0x00000a74   0x00000a74   0x0000000a   Code   RO          221    .text.DL_Common_delayCycles  driverlib.a(dl_common.o)
    0x00000a7e   0x00000a7e   0x00000002   PAD
    0x00000a80   0x00000a80   0x00000044   Code   RO          230    .text.DL_DMA_initChannel  driverlib.a(dl_dma.o)
    0x00000ac4   0x00000ac4   0x00000026   Code   RO          240    .text.DL_I2C_setClockConfig  driverlib.a(dl_i2c.o)
    0x00000aea   0x00000aea   0x00000002   PAD
    0x00000aec   0x00000aec   0x00000110   Code   RO          352    .text.DL_Timer_initFourCCPWMMode  driverlib.a(dl_timer.o)
    0x00000bfc   0x00000bfc   0x0000001c   Code   RO          318    .text.DL_Timer_setCaptCompUpdateMethod  driverlib.a(dl_timer.o)
    0x00000c18   0x00000c18   0x00000018   Code   RO          326    .text.DL_Timer_setCaptureCompareOutCtl  driverlib.a(dl_timer.o)
    0x00000c30   0x00000c30   0x00000010   Code   RO          278    .text.DL_Timer_setCaptureCompareValue  driverlib.a(dl_timer.o)
    0x00000c40   0x00000c40   0x0000001c   Code   RO          272    .text.DL_Timer_setClockConfig  driverlib.a(dl_timer.o)
    0x00000c5c   0x00000c5c   0x00000028   Code   RO          393    .text.DL_UART_drainRXFIFO  driverlib.a(dl_uart.o)
    0x00000c84   0x00000c84   0x00000048   Code   RO          373    .text.DL_UART_init  driverlib.a(dl_uart.o)
    0x00000ccc   0x00000ccc   0x00000012   Code   RO          375    .text.DL_UART_setClockConfig  driverlib.a(dl_uart.o)
    0x00000cde   0x00000cde   0x00000002   PAD
    0x00000ce0   0x00000ce0   0x00000244   Code   RO          160    .text.Motor_LineFollowing  motor.o
    0x00000f24   0x00000f24   0x00000008   Code   RO          158    .text.Motor_Proc    motor.o
    0x00000f2c   0x00000f2c   0x00000028   Code   RO           48    .text.SYSCFG_DL_DMA_WIT_init  ti_msp_dl_config.o
    0x00000f54   0x00000f54   0x00000008   Code   RO           40    .text.SYSCFG_DL_DMA_init  ti_msp_dl_config.o
    0x00000f5c   0x00000f5c   0x00000080   Code   RO           26    .text.SYSCFG_DL_GPIO_init  ti_msp_dl_config.o
    0x00000fdc   0x00000fdc   0x00000060   Code   RO           34    .text.SYSCFG_DL_I2C_OLED_init  ti_msp_dl_config.o
    0x0000103c   0x0000103c   0x0000005c   Code   RO           30    .text.SYSCFG_DL_PWM_MOTOR1_init  ti_msp_dl_config.o
    0x00001098   0x00001098   0x0000005c   Code   RO           32    .text.SYSCFG_DL_PWM_MOTOR2_init  ti_msp_dl_config.o
    0x000010f4   0x000010f4   0x00000040   Code   RO           28    .text.SYSCFG_DL_SYSCTL_init  ti_msp_dl_config.o
    0x00001134   0x00001134   0x00000030   Code   RO           42    .text.SYSCFG_DL_SYSTICK_init  ti_msp_dl_config.o
    0x00001164   0x00001164   0x0000005c   Code   RO           38    .text.SYSCFG_DL_UART_0_init  ti_msp_dl_config.o
    0x000011c0   0x000011c0   0x00000090   Code   RO           36    .text.SYSCFG_DL_UART_WIT_init  ti_msp_dl_config.o
    0x00001250   0x00001250   0x00000040   Code   RO           22    .text.SYSCFG_DL_init  ti_msp_dl_config.o
    0x00001290   0x00001290   0x00000060   Code   RO           24    .text.SYSCFG_DL_initPower  ti_msp_dl_config.o
    0x000012f0   0x000012f0   0x00000014   Code   RO          209    .text.SysTick_Handler  systick.o
    0x00001304   0x00001304   0x00000008   Code   RO          184    .text.Task_Init     task.o
    0x0000130c   0x0000130c   0x0000002c   Code   RO          186    .text.Task_Marks    task.o
    0x00001338   0x00001338   0x00000034   Code   RO          188    .text.Task_Proc     task.o
    0x0000136c   0x0000136c   0x00000240   Code   RO          146    .text.UART1_IRQHandler  wit.o
    0x000015ac   0x000015ac   0x00000030   Code   RO          144    .text.WIT_Init      wit.o
    0x000015dc   0x000015dc   0x00000010   Code   RO          201    .text.led_toggle    systick.o
    0x000015ec   0x000015ec   0x00000020   Code   RO            2    .text.main          empty.o
    0x0000160c   0x0000160c   0x000001f6   Code   RO          419    .text_divfast       c_p.l(aeabi_sdivfast.o)
    0x00001802   0x00001802   0x00000000   Code   RO          482    x$fpl$usenofp       fz_ps.l(usenofp.o)
    0x00001802   0x00001802   0x00000080   Data   RO          435    .constdata          fz_ps.l(ddiv.o)
    0x00001882   0x00001882   0x00000002   PAD
    0x00001884   0x00001884   0x00000018   Data   RO           61    .rodata.gDMA_WITConfig  ti_msp_dl_config.o
    0x0000189c   0x0000189c   0x00000002   Data   RO           56    .rodata.gI2C_OLEDClockConfig  ti_msp_dl_config.o
    0x0000189e   0x0000189e   0x00000003   Data   RO           52    .rodata.gPWM_MOTOR1ClockConfig  ti_msp_dl_config.o
    0x000018a1   0x000018a1   0x00000003   PAD
    0x000018a4   0x000018a4   0x00000008   Data   RO           53    .rodata.gPWM_MOTOR1Config  ti_msp_dl_config.o
    0x000018ac   0x000018ac   0x00000003   Data   RO           54    .rodata.gPWM_MOTOR2ClockConfig  ti_msp_dl_config.o
    0x000018af   0x000018af   0x00000001   PAD
    0x000018b0   0x000018b0   0x00000008   Data   RO           55    .rodata.gPWM_MOTOR2Config  ti_msp_dl_config.o
    0x000018b8   0x000018b8   0x00000002   Data   RO           59    .rodata.gUART_0ClockConfig  ti_msp_dl_config.o
    0x000018ba   0x000018ba   0x0000000a   Data   RO           60    .rodata.gUART_0Config  ti_msp_dl_config.o
    0x000018c4   0x000018c4   0x00000002   Data   RO           57    .rodata.gUART_WITClockConfig  ti_msp_dl_config.o
    0x000018c6   0x000018c6   0x0000000a   Data   RO           58    .rodata.gUART_WITConfig  ti_msp_dl_config.o
    0x000018d0   0x000018d0   0x00000020   Data   RO          658    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x20200000, Load base: 0x000018f0, Size: 0x0000101c, Max: 0x00008000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20200000   0x000018f0   0x00000018   Data   RW          191    .data.MyTask        task.o
    0x20200018        -       0x00000060   Zero   RW          522    .bss                c_p.l(libspace.o)
    0x20200078        -       0x000000bc   Zero   RW           50    .bss.gPWM_MOTOR1Backup  ti_msp_dl_config.o
    0x20200134        -       0x000000bc   Zero   RW           51    .bss.gPWM_MOTOR2Backup  ti_msp_dl_config.o
    0x202001f0        -       0x00000001   Zero   RW          190    .bss.task_num       task.o
    0x202001f1   0x00001908   0x00000003   PAD
    0x202001f4        -       0x00000004   Zero   RW          211    .bss.tick_ms        systick.o
    0x202001f8        -       0x00000020   Zero   RW          149    .bss.wit_data       wit.o
    0x20200218        -       0x00000021   Zero   RW          148    .bss.wit_dmaBuffer  wit.o
    0x20200239   0x00001908   0x00000007   PAD
    0x20200240        -       0x00000800   Zero   RW           13    HEAP                startup_mspm0g350x_uvision.o
    0x20200a40        -       0x000005dc   Zero   RW           12    STACK               startup_mspm0g350x_uvision.o



  Load Region LR_BCR (Base: 0x41c00000, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BCR_CONFIG (Exec base: 0x41c00000, Load base: 0x41c00000, Size: 0x00000000, Max: 0x000000ff, ABSOLUTE)

    **** No section assigned to this execution region ****



  Load Region LR_BSL (Base: 0x41c00100, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BSL_CONFIG (Exec base: 0x41c00100, Load base: 0x41c00100, Size: 0x00000000, Max: 0x00000080, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

        32          4          0          0          0       3455   empty.o
       740        136          0          0          0      16863   motor.o
        48         22        192          0       3548        640   startup_mspm0g350x_uvision.o
        36          8          0          0          4       6003   systick.o
       104          8          0         24          1       1220   task.o
       964        192         72          0        376      31914   ti_msp_dl_config.o
       624         48          0          0         65       7098   wit.o

    ----------------------------------------------------------------------
      2548        <USER>        <GROUP>         24       4004      67193   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         0          0          4          0         10          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        60          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
       502          0          0          0          0         92   aeabi_sdivfast.o
        16          0          0          0          0         68   exit.o
         6          0          0          0          0        136   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        12          4          0          0          0         60   sys_exit.o
        62          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        10          0          0          0          0        803   dl_common.o
        68          4          0          0          0       4510   dl_dma.o
        38          0          0          0          0       8619   dl_i2c.o
       368         44          0          0          0      41556   dl_timer.o
       130         12          0          0          0      14163   dl_uart.o
       124          4          0          0          0         72   d2f.o
      1096         26        128          0          0        112   ddiv.o
       108         10          0          0          0         72   dfixi.o
        88          0          0          0          0         92   dflti.o
       584         26          0          0          0         84   dmul.o
         0          0          0          0          0          0   usenofp.o

    ----------------------------------------------------------------------
      3406        <USER>        <GROUP>          0         96      70723   Library Totals
        24          4          2          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       768         16          0          0         96        640   c_p.l
       614         60          0          0          0      69651   driverlib.a
      2000         66        128          0          0        432   fz_ps.l

    ----------------------------------------------------------------------
      3406        <USER>        <GROUP>          0         96      70723   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      5954        564        430         24       4100     137392   Grand Totals
      5954        564        430         24       4100     137392   ELF Image Totals
      5954        564        430         24          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 6384 (   6.23kB)
    Total RW  Size (RW Data + ZI Data)              4124 (   4.03kB)
    Total ROM Size (Code + RO Data + RW Data)       6408 (   6.26kB)

==============================================================================

