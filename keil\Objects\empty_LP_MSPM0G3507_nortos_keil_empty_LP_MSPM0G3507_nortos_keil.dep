Dependencies for Project 'empty_LP_MSPM0G3507_nortos_keil', Target 'empty_LP_MSPM0G3507_nortos_keil': (DO NOT MODIFY !)
CompilerVersion: 6160000::V6.16::ARMCLANG
F (../empty.c)(0x688C15EB)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../ -I D:/TI/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include -I D:/TI/mspm0_sdk_2_05_01_00/source -I ../../empty -I ./Hardware -I ./System -I ./Use

-D__UVISION_VERSION="534" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/empty.o -MD)
I (..\ti_msp_dl_config.h)(0x688C1386)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\msp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\DeviceFamily.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6035A4A8)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x5EE13552)
I (D:\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6035A4A4)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_adc12.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_aes.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_comp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_crc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dma.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mcan.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_oa.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_rtc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_spi.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_trng.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_uart.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_vref.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wuc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\driverlib.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_adc12.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\math.h)(0x6035A4A8)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_common.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_factoryregion.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_core.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aes.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aesadv.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_comp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crcp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dac12.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dma.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_flashctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_sysctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpamp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpio.h)(0x688A2619)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_i2c.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_iwdt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lfss.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_keystorectl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lcd.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mathacl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mcan.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_opa.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_common.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_a.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_b.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_scratchpad.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_spi.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_tamperio.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timera.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timer.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timerg.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_trng.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_extend.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_main.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_vref.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_wwdt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_interrupt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_systick.h)(0x6866DDBA)
I (Use\task.h)(0x688C1878)
I (Hardware\Delay.h)(0x6888DA52)
I (Hardware\Uart.h)(0x6881FB06)
I (Hardware\Oled_Hardware.h)(0x68863ECC)
I (System\Systick.h)(0x688B3BA4)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x6035A4A8)
I (Hardware\wit.h)(0x6885AD24)
I (Use\Motor.h)(0x688BA785)
I (Hardware\Key.h)(0x688C1541)
I (Use\KeyApp.h)(0x688C15B7)
F (../empty.syscfg)(0x688C1386)()
F (startup_mspm0g350x_uvision.s)(0x68860AE6)(--target=arm-arm-none-eabi -mcpu=cortex-m0plus -masm=auto -c

-gdwarf-3

-Wa,armasm,--pd,"__UVISION_VERSION SETA 534" -Wa,armasm,--pd,"__MSPM0G3507__ SETA 1"

-o ./objects/startup_mspm0g350x_uvision.o)
F (../ti_msp_dl_config.h)(0x688C1386)()
F (../ti_msp_dl_config.c)(0x688C1386)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../ -I D:/TI/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include -I D:/TI/mspm0_sdk_2_05_01_00/source -I ../../empty -I ./Hardware -I ./System -I ./Use

-D__UVISION_VERSION="534" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/ti_msp_dl_config.o -MD)
I (..\ti_msp_dl_config.h)(0x688C1386)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\msp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\DeviceFamily.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6035A4A8)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x5EE13552)
I (D:\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6035A4A4)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_adc12.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_aes.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_comp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_crc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dma.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mcan.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_oa.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_rtc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_spi.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_trng.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_uart.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_vref.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wuc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\driverlib.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_adc12.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\math.h)(0x6035A4A8)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_common.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_factoryregion.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_core.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aes.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aesadv.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_comp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crcp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dac12.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dma.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_flashctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_sysctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpamp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpio.h)(0x688A2619)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_i2c.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_iwdt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lfss.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_keystorectl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lcd.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mathacl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mcan.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_opa.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_common.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_a.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_b.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_scratchpad.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_spi.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_tamperio.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timera.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timer.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timerg.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_trng.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_extend.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_main.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_vref.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_wwdt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_interrupt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_systick.h)(0x6866DDBA)
F (.\Hardware\Delay.c)(0x6888DA52)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../ -I D:/TI/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include -I D:/TI/mspm0_sdk_2_05_01_00/source -I ../../empty -I ./Hardware -I ./System -I ./Use

-D__UVISION_VERSION="534" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/delay.o -MD)
I (..\ti_msp_dl_config.h)(0x688C1386)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\msp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\DeviceFamily.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6035A4A8)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x5EE13552)
I (D:\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6035A4A4)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_adc12.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_aes.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_comp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_crc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dma.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mcan.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_oa.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_rtc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_spi.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_trng.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_uart.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_vref.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wuc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\driverlib.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_adc12.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\math.h)(0x6035A4A8)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_common.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_factoryregion.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_core.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aes.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aesadv.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_comp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crcp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dac12.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dma.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_flashctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_sysctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpamp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpio.h)(0x688A2619)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_i2c.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_iwdt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lfss.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_keystorectl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lcd.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mathacl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mcan.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_opa.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_common.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_a.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_b.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_scratchpad.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_spi.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_tamperio.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timera.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timer.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timerg.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_trng.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_extend.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_main.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_vref.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_wwdt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_interrupt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_systick.h)(0x6866DDBA)
I (Hardware\Delay.h)(0x6888DA52)
F (.\Hardware\Delay.h)(0x6888DA52)()
F (.\Hardware\Uart.c)(0x688215A5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../ -I D:/TI/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include -I D:/TI/mspm0_sdk_2_05_01_00/source -I ../../empty -I ./Hardware -I ./System -I ./Use

-D__UVISION_VERSION="534" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/uart.o -MD)
I (..\ti_msp_dl_config.h)(0x688C1386)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\msp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\DeviceFamily.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6035A4A8)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x5EE13552)
I (D:\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6035A4A4)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_adc12.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_aes.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_comp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_crc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dma.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mcan.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_oa.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_rtc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_spi.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_trng.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_uart.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_vref.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wuc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\driverlib.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_adc12.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\math.h)(0x6035A4A8)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_common.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_factoryregion.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_core.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aes.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aesadv.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_comp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crcp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dac12.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dma.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_flashctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_sysctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpamp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpio.h)(0x688A2619)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_i2c.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_iwdt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lfss.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_keystorectl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lcd.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mathacl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mcan.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_opa.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_common.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_a.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_b.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_scratchpad.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_spi.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_tamperio.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timera.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timer.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timerg.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_trng.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_extend.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_main.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_vref.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_wwdt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_interrupt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_systick.h)(0x6866DDBA)
I (Hardware\Uart.h)(0x6881FB06)
F (.\Hardware\Uart.h)(0x6881FB06)()
F (.\Hardware\Oled_Hardware.c)(0x68863ECC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../ -I D:/TI/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include -I D:/TI/mspm0_sdk_2_05_01_00/source -I ../../empty -I ./Hardware -I ./System -I ./Use

-D__UVISION_VERSION="534" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/oled_hardware.o -MD)
I (Hardware\Oled_Hardware.h)(0x68863ECC)
I (..\ti_msp_dl_config.h)(0x688C1386)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\msp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\DeviceFamily.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6035A4A8)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x5EE13552)
I (D:\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6035A4A4)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_adc12.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_aes.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_comp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_crc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dma.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mcan.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_oa.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_rtc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_spi.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_trng.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_uart.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_vref.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wuc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\driverlib.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_adc12.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\math.h)(0x6035A4A8)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_common.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_factoryregion.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_core.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aes.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aesadv.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_comp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crcp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dac12.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dma.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_flashctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_sysctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpamp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpio.h)(0x688A2619)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_i2c.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_iwdt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lfss.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_keystorectl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lcd.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mathacl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mcan.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_opa.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_common.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_a.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_b.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_scratchpad.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_spi.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_tamperio.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timera.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timer.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timerg.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_trng.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_extend.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_main.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_vref.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_wwdt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_interrupt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_systick.h)(0x6866DDBA)
I (Hardware\oledfont.h)(0x68831904)
I (System\Systick.h)(0x688B3BA4)
I (Hardware\wit.h)(0x6885AD24)
F (.\Hardware\Oled_Hardware.h)(0x68863ECC)()
F (.\Hardware\oledfont.h)(0x68831904)()
F (.\Hardware\wit.c)(0x6885E89C)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../ -I D:/TI/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include -I D:/TI/mspm0_sdk_2_05_01_00/source -I ../../empty -I ./Hardware -I ./System -I ./Use

-D__UVISION_VERSION="534" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/wit.o -MD)
I (Hardware\wit.h)(0x6885AD24)
I (..\ti_msp_dl_config.h)(0x688C1386)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\msp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\DeviceFamily.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6035A4A8)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x5EE13552)
I (D:\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6035A4A4)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_adc12.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_aes.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_comp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_crc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dma.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mcan.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_oa.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_rtc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_spi.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_trng.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_uart.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_vref.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wuc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\driverlib.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_adc12.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\math.h)(0x6035A4A8)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_common.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_factoryregion.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_core.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aes.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aesadv.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_comp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crcp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dac12.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dma.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_flashctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_sysctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpamp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpio.h)(0x688A2619)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_i2c.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_iwdt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lfss.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_keystorectl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lcd.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mathacl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mcan.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_opa.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_common.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_a.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_b.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_scratchpad.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_spi.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_tamperio.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timera.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timer.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timerg.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_trng.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_extend.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_main.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_vref.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_wwdt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_interrupt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_systick.h)(0x6866DDBA)
F (.\Hardware\wit.h)(0x6885AD24)()
F (.\Hardware\Key.c)(0x688C1702)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../ -I D:/TI/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include -I D:/TI/mspm0_sdk_2_05_01_00/source -I ../../empty -I ./Hardware -I ./System -I ./Use

-D__UVISION_VERSION="534" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/key.o -MD)
I (Hardware\Key.h)(0x688C1541)
I (..\ti_msp_dl_config.h)(0x688C1386)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\msp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\DeviceFamily.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6035A4A8)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x5EE13552)
I (D:\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6035A4A4)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_adc12.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_aes.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_comp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_crc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dma.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mcan.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_oa.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_rtc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_spi.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_trng.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_uart.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_vref.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wuc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\driverlib.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_adc12.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\math.h)(0x6035A4A8)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_common.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_factoryregion.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_core.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aes.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aesadv.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_comp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crcp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dac12.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dma.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_flashctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_sysctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpamp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpio.h)(0x688A2619)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_i2c.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_iwdt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lfss.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_keystorectl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lcd.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mathacl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mcan.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_opa.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_common.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_a.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_b.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_scratchpad.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_spi.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_tamperio.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timera.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timer.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timerg.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_trng.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_extend.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_main.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_vref.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_wwdt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_interrupt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_systick.h)(0x6866DDBA)
I (Hardware\Delay.h)(0x6888DA52)
F (.\Hardware\Key.h)(0x688C1541)()
F (.\Use\Motor.c)(0x688C15CF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../ -I D:/TI/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include -I D:/TI/mspm0_sdk_2_05_01_00/source -I ../../empty -I ./Hardware -I ./System -I ./Use

-D__UVISION_VERSION="534" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/motor.o -MD)
I (..\ti_msp_dl_config.h)(0x688C1386)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\msp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\DeviceFamily.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6035A4A8)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x5EE13552)
I (D:\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6035A4A4)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_adc12.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_aes.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_comp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_crc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dma.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mcan.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_oa.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_rtc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_spi.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_trng.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_uart.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_vref.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wuc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\driverlib.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_adc12.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\math.h)(0x6035A4A8)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_common.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_factoryregion.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_core.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aes.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aesadv.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_comp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crcp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dac12.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dma.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_flashctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_sysctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpamp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpio.h)(0x688A2619)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_i2c.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_iwdt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lfss.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_keystorectl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lcd.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mathacl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mcan.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_opa.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_common.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_a.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_b.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_scratchpad.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_spi.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_tamperio.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timera.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timer.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timerg.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_trng.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_extend.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_main.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_vref.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_wwdt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_interrupt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_systick.h)(0x6866DDBA)
I (Use\Motor.h)(0x688BA785)
I (Use\KeyApp.h)(0x688C15B7)
F (.\Use\Motor.h)(0x688BA785)()
F (.\Use\task.c)(0x688C158F)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../ -I D:/TI/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include -I D:/TI/mspm0_sdk_2_05_01_00/source -I ../../empty -I ./Hardware -I ./System -I ./Use

-D__UVISION_VERSION="534" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/task.o -MD)
I (..\ti_msp_dl_config.h)(0x688C1386)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\msp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\DeviceFamily.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6035A4A8)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x5EE13552)
I (D:\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6035A4A4)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_adc12.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_aes.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_comp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_crc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dma.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mcan.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_oa.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_rtc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_spi.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_trng.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_uart.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_vref.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wuc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\driverlib.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_adc12.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\math.h)(0x6035A4A8)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_common.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_factoryregion.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_core.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aes.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aesadv.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_comp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crcp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dac12.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dma.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_flashctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_sysctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpamp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpio.h)(0x688A2619)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_i2c.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_iwdt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lfss.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_keystorectl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lcd.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mathacl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mcan.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_opa.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_common.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_a.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_b.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_scratchpad.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_spi.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_tamperio.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timera.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timer.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timerg.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_trng.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_extend.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_main.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_vref.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_wwdt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_interrupt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_systick.h)(0x6866DDBA)
I (Use\task.h)(0x688C1878)
I (Hardware\Delay.h)(0x6888DA52)
I (Hardware\Uart.h)(0x6881FB06)
I (Hardware\Oled_Hardware.h)(0x68863ECC)
I (System\Systick.h)(0x688B3BA4)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x6035A4A8)
I (Hardware\wit.h)(0x6885AD24)
I (Use\Motor.h)(0x688BA785)
I (Hardware\Key.h)(0x688C1541)
F (.\Use\task.h)(0x688C1878)()
F (.\Use\KeyApp.c)(0x688C1869)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../ -I D:/TI/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include -I D:/TI/mspm0_sdk_2_05_01_00/source -I ../../empty -I ./Hardware -I ./System -I ./Use

-D__UVISION_VERSION="534" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/keyapp.o -MD)
I (..\ti_msp_dl_config.h)(0x688C1386)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\msp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\DeviceFamily.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6035A4A8)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x5EE13552)
I (D:\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6035A4A4)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_adc12.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_aes.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_comp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_crc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dma.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mcan.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_oa.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_rtc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_spi.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_trng.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_uart.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_vref.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wuc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\driverlib.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_adc12.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\math.h)(0x6035A4A8)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_common.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_factoryregion.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_core.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aes.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aesadv.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_comp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crcp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dac12.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dma.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_flashctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_sysctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpamp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpio.h)(0x688A2619)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_i2c.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_iwdt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lfss.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_keystorectl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lcd.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mathacl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mcan.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_opa.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_common.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_a.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_b.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_scratchpad.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_spi.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_tamperio.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timera.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timer.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timerg.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_trng.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_extend.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_main.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_vref.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_wwdt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_interrupt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_systick.h)(0x6866DDBA)
I (Use\Motor.h)(0x688BA785)
I (Hardware\Key.h)(0x688C1541)
F (.\Use\KeyApp.h)(0x688C15B7)()
F (.\System\Systick.c)(0x688B4332)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../ -I D:/TI/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include -I D:/TI/mspm0_sdk_2_05_01_00/source -I ../../empty -I ./Hardware -I ./System -I ./Use

-D__UVISION_VERSION="534" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/systick.o -MD)
I (..\ti_msp_dl_config.h)(0x688C1386)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\msp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\DeviceFamily.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6035A4A8)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x5EE13552)
I (D:\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6035A4A4)
I (D:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_adc12.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_aes.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_comp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_crc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dma.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mcan.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_oa.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_rtc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_spi.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_trng.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_uart.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_vref.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wuc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\driverlib.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_adc12.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\math.h)(0x6035A4A8)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_common.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_factoryregion.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_core.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aes.h)(0x6866DDBA)
I (D:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aesadv.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_comp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crcp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dac12.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dma.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_flashctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_sysctl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpamp.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpio.h)(0x688A2619)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_i2c.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_iwdt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lfss.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_keystorectl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lcd.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mathacl.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mcan.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_opa.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_common.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_a.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_b.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_scratchpad.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_spi.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_tamperio.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timera.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timer.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timerg.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_trng.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_extend.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_main.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_vref.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_wwdt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_interrupt.h)(0x6866DDBA)
I (D:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_systick.h)(0x6866DDBA)
I (System\Systick.h)(0x688B3BA4)
I (Use\task.h)(0x688C1878)
I (Hardware\Delay.h)(0x6888DA52)
I (Hardware\Uart.h)(0x6881FB06)
I (Hardware\Oled_Hardware.h)(0x68863ECC)
I (D:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x6035A4A8)
I (Hardware\wit.h)(0x6885AD24)
I (Use\Motor.h)(0x688BA785)
I (Hardware\Key.h)(0x688C1541)
F (.\System\Systick.h)(0x688B3BA4)()
