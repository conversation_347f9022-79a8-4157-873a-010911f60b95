#ifndef __MOTOR_H__
#define __MOTOR_H__

#include "ti_msp_dl_config.h"

void Motor_Proc(void);
void Motor(unsigned char ch ,int speed);

// 基础电机控制函数声明
void Motor1_SetSpeed(uint32_t speed_hz); // 设置电机1速度
void Motor2_SetSpeed(uint32_t speed_hz); // 设置电机2速度

// 差速转向控制函数声明
void Motor_DifferentialControl(uint32_t base_speed, int16_t turn_value); // 差速转向控制
void Motor_Move(uint32_t speed, int8_t direction); // 简化方向控制
void Motor_Stop(void); // 停止所有电机

#endif
