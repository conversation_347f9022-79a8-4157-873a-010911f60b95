#ifndef __MOTOR_H__
#define __MOTOR_H__

#include "ti_msp_dl_config.h"

void Motor_Proc(void);
void Motor(unsigned char ch ,int speed);

// 基础电机控制函数声明
void Motor1_SetSpeed(uint32_t speed_hz); // 设置电机1速度
void Motor2_SetSpeed(uint32_t speed_hz); // 设置电机2速度

// 差速转向控制函数声明
void Motor_DifferentialControl(uint32_t base_speed, int16_t turn_value); // 差速转向控制
void Motor_Move(uint32_t speed, int8_t direction); // 简化方向控制
void Motor_Stop(void); // 停止所有电机

// 寻迹巡线功能函数声明
uint8_t Read_GraySensors(void); // 读取7路灰度传感器
int8_t Calculate_LinePosition(uint8_t sensor_data); // 计算线位置偏差
void Motor_LineFollowing(void); // 寻迹巡线主函数

#endif
