/*
 * Copyright (c) 2023, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, IN<PERSON>DENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/*
 *  ============ ti_msp_dl_config.h =============
 *  Configured MSPM0 DriverLib module declarations
 *
 *  DO NOT EDIT - This file is generated for the MSPM0G350X
 *  by the SysConfig tool.
 */
#ifndef ti_msp_dl_config_h
#define ti_msp_dl_config_h

#define CONFIG_MSPM0G350X

#if defined(__ti_version__) || defined(__TI_COMPILER_VERSION__)
#define SYSCONFIG_WEAK __attribute__((weak))
#elif defined(__IAR_SYSTEMS_ICC__)
#define SYSCONFIG_WEAK __weak
#elif defined(__GNUC__)
#define SYSCONFIG_WEAK __attribute__((weak))
#endif

#include <ti/devices/msp/msp.h>
#include <ti/driverlib/driverlib.h>
#include <ti/driverlib/m0p/dl_core.h>
#include <ti/driverlib/dl_timer.h>
#include <ti/driverlib/dl_timera.h>

#ifdef __cplusplus
extern "C" {
#endif

/*
 *  ======== SYSCFG_DL_init ========
 *  Perform all required MSP DL initialization
 *
 *  This function should be called once at a point before any use of
 *  MSP DL.
 */


/* clang-format off */

#define POWER_STARTUP_DELAY                                                (16)


#define CPUCLK_FREQ                                                     32000000



/* Defines for PWM_MOTOR1 */
#define PWM_MOTOR1_INST                                                    TIMA0
#define PWM_MOTOR1_INST_IRQHandler                              TIMA0_IRQHandler
#define PWM_MOTOR1_INST_INT_IRQN                                (TIMA0_INT_IRQn)
#define PWM_MOTOR1_INST_CLK_FREQ                                         1000000
/* GPIO defines for channel 2 */
#define GPIO_PWM_MOTOR1_C2_PORT                                            GPIOB
#define GPIO_PWM_MOTOR1_C2_PIN                                     DL_GPIO_PIN_4
#define GPIO_PWM_MOTOR1_C2_IOMUX                                 (IOMUX_PINCM17)
#define GPIO_PWM_MOTOR1_C2_IOMUX_FUNC                IOMUX_PINCM17_PF_TIMA0_CCP2
#define GPIO_PWM_MOTOR1_C2_IDX                               DL_TIMER_CC_2_INDEX

/* Defines for PWM_MOTOR2 */
#define PWM_MOTOR2_INST                                                    TIMA1
#define PWM_MOTOR2_INST_IRQHandler                              TIMA1_IRQHandler
#define PWM_MOTOR2_INST_INT_IRQN                                (TIMA1_INT_IRQn)
#define PWM_MOTOR2_INST_CLK_FREQ                                         1000000
/* GPIO defines for channel 1 */
#define GPIO_PWM_MOTOR2_C1_PORT                                            GPIOB
#define GPIO_PWM_MOTOR2_C1_PIN                                     DL_GPIO_PIN_5
#define GPIO_PWM_MOTOR2_C1_IOMUX                                 (IOMUX_PINCM18)
#define GPIO_PWM_MOTOR2_C1_IOMUX_FUNC                IOMUX_PINCM18_PF_TIMA1_CCP1
#define GPIO_PWM_MOTOR2_C1_IDX                               DL_TIMER_CC_1_INDEX




/* Defines for I2C_OLED */
#define I2C_OLED_INST                                                       I2C0
#define I2C_OLED_INST_IRQHandler                                 I2C0_IRQHandler
#define I2C_OLED_INST_INT_IRQN                                     I2C0_INT_IRQn
#define I2C_OLED_BUS_SPEED_HZ                                             400000
#define GPIO_I2C_OLED_SDA_PORT                                             GPIOA
#define GPIO_I2C_OLED_SDA_PIN                                     DL_GPIO_PIN_28
#define GPIO_I2C_OLED_IOMUX_SDA                                   (IOMUX_PINCM3)
#define GPIO_I2C_OLED_IOMUX_SDA_FUNC                    IOMUX_PINCM3_PF_I2C0_SDA
#define GPIO_I2C_OLED_SCL_PORT                                             GPIOA
#define GPIO_I2C_OLED_SCL_PIN                                     DL_GPIO_PIN_31
#define GPIO_I2C_OLED_IOMUX_SCL                                   (IOMUX_PINCM6)
#define GPIO_I2C_OLED_IOMUX_SCL_FUNC                    IOMUX_PINCM6_PF_I2C0_SCL


/* Defines for UART_WIT */
#define UART_WIT_INST                                                      UART1
#define UART_WIT_INST_IRQHandler                                UART1_IRQHandler
#define UART_WIT_INST_INT_IRQN                                    UART1_INT_IRQn
#define GPIO_UART_WIT_RX_PORT                                              GPIOA
#define GPIO_UART_WIT_RX_PIN                                       DL_GPIO_PIN_9
#define GPIO_UART_WIT_IOMUX_RX                                   (IOMUX_PINCM20)
#define GPIO_UART_WIT_IOMUX_RX_FUNC                    IOMUX_PINCM20_PF_UART1_RX
#define UART_WIT_BAUD_RATE                                              (115200)
#define UART_WIT_IBRD_32_MHZ_115200_BAUD                                    (17)
#define UART_WIT_FBRD_32_MHZ_115200_BAUD                                    (23)
/* Defines for UART_0 */
#define UART_0_INST                                                        UART0
#define UART_0_INST_IRQHandler                                  UART0_IRQHandler
#define UART_0_INST_INT_IRQN                                      UART0_INT_IRQn
#define GPIO_UART_0_RX_PORT                                                GPIOA
#define GPIO_UART_0_TX_PORT                                                GPIOA
#define GPIO_UART_0_RX_PIN                                        DL_GPIO_PIN_11
#define GPIO_UART_0_TX_PIN                                        DL_GPIO_PIN_10
#define GPIO_UART_0_IOMUX_RX                                     (IOMUX_PINCM22)
#define GPIO_UART_0_IOMUX_TX                                     (IOMUX_PINCM21)
#define GPIO_UART_0_IOMUX_RX_FUNC                      IOMUX_PINCM22_PF_UART0_RX
#define GPIO_UART_0_IOMUX_TX_FUNC                      IOMUX_PINCM21_PF_UART0_TX
#define UART_0_BAUD_RATE                                                (115200)
#define UART_0_IBRD_32_MHZ_115200_BAUD                                      (17)
#define UART_0_FBRD_32_MHZ_115200_BAUD                                      (23)





/* Defines for DMA_WIT */
#define DMA_WIT_CHAN_ID                                                      (0)
#define UART_WIT_INST_DMA_TRIGGER                            (DMA_UART1_RX_TRIG)



/* Port definition for Pin Group GPIO_LED */
#define GPIO_LED_PORT                                                    (GPIOB)

/* Defines for PIN_LED: GPIOB.22 with pinCMx 50 on package pin 21 */
#define GPIO_LED_PIN_LED_PIN                                    (DL_GPIO_PIN_22)
#define GPIO_LED_PIN_LED_IOMUX                                   (IOMUX_PINCM50)
/* Defines for PIN_3: GPIOA.15 with pinCMx 37 on package pin 8 */
#define GPIO_GRAY_PIN_3_PORT                                             (GPIOA)
#define GPIO_GRAY_PIN_3_PIN                                     (DL_GPIO_PIN_15)
#define GPIO_GRAY_PIN_3_IOMUX                                    (IOMUX_PINCM37)
/* Defines for PIN_1: GPIOA.17 with pinCMx 39 on package pin 10 */
#define GPIO_GRAY_PIN_1_PORT                                             (GPIOA)
#define GPIO_GRAY_PIN_1_PIN                                     (DL_GPIO_PIN_17)
#define GPIO_GRAY_PIN_1_IOMUX                                    (IOMUX_PINCM39)
/* Defines for PIN_2: GPIOA.14 with pinCMx 36 on package pin 7 */
#define GPIO_GRAY_PIN_2_PORT                                             (GPIOA)
#define GPIO_GRAY_PIN_2_PIN                                     (DL_GPIO_PIN_14)
#define GPIO_GRAY_PIN_2_IOMUX                                    (IOMUX_PINCM36)
/* Defines for PIN_4: GPIOB.20 with pinCMx 48 on package pin 19 */
#define GPIO_GRAY_PIN_4_PORT                                             (GPIOB)
#define GPIO_GRAY_PIN_4_PIN                                     (DL_GPIO_PIN_20)
#define GPIO_GRAY_PIN_4_IOMUX                                    (IOMUX_PINCM48)
/* Defines for PIN_5: GPIOA.22 with pinCMx 47 on package pin 18 */
#define GPIO_GRAY_PIN_5_PORT                                             (GPIOA)
#define GPIO_GRAY_PIN_5_PIN                                     (DL_GPIO_PIN_22)
#define GPIO_GRAY_PIN_5_IOMUX                                    (IOMUX_PINCM47)
/* Defines for PIN_6: GPIOB.25 with pinCMx 56 on package pin 27 */
#define GPIO_GRAY_PIN_6_PORT                                             (GPIOB)
#define GPIO_GRAY_PIN_6_PIN                                     (DL_GPIO_PIN_25)
#define GPIO_GRAY_PIN_6_IOMUX                                    (IOMUX_PINCM56)
/* Defines for PIN_7: GPIOB.24 with pinCMx 52 on package pin 23 */
#define GPIO_GRAY_PIN_7_PORT                                             (GPIOB)
#define GPIO_GRAY_PIN_7_PIN                                     (DL_GPIO_PIN_24)
#define GPIO_GRAY_PIN_7_IOMUX                                    (IOMUX_PINCM52)



/* clang-format on */

void SYSCFG_DL_init(void);
void SYSCFG_DL_initPower(void);
void SYSCFG_DL_GPIO_init(void);
void SYSCFG_DL_SYSCTL_init(void);
void SYSCFG_DL_PWM_MOTOR1_init(void);
void SYSCFG_DL_PWM_MOTOR2_init(void);
void SYSCFG_DL_I2C_OLED_init(void);
void SYSCFG_DL_UART_WIT_init(void);
void SYSCFG_DL_UART_0_init(void);
void SYSCFG_DL_DMA_init(void);

void SYSCFG_DL_SYSTICK_init(void);

bool SYSCFG_DL_saveConfiguration(void);
bool SYSCFG_DL_restoreConfiguration(void);

#ifdef __cplusplus
}
#endif

#endif /* ti_msp_dl_config_h */
