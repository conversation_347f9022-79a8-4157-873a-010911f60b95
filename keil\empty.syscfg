/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --package "LQFP-64(PM)" --part "Default" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.20.0+3587"}
 */

/**
 * Import the modules used in this configuration.
 */
const GPIO    = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1   = GPIO.addInstance();
const I2C     = scripting.addModule("/ti/driverlib/I2C", {}, false);
const I2C1    = I2C.addInstance();
const PWM     = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1    = PWM.addInstance();
const PWM2    = PWM.addInstance();
const PWM3    = PWM.addInstance();
const SYSCTL  = scripting.addModule("/ti/driverlib/SYSCTL");
const SYSTICK = scripting.addModule("/ti/driverlib/SYSTICK");
const UART    = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1   = UART.addInstance();
const UART2   = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
GPIO1.$name                          = "STEP_MOTOR";
GPIO1.associatedPins.create(2);
GPIO1.associatedPins[0].$name        = "DIR1";
GPIO1.associatedPins[0].assignedPort = "PORTB";
GPIO1.associatedPins[0].pin.$assign  = "PB4";
GPIO1.associatedPins[1].$name        = "DIR2";
GPIO1.associatedPins[1].assignedPort = "PORTB";
GPIO1.associatedPins[1].pin.$assign  = "PB5";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

I2C1.$name                             = "I2C_OLED";
I2C1.basicEnableController             = true;
I2C1.basicControllerBusSpeed           = 400000;
I2C1.intController                     = ["NACK","RXFIFO_TRIGGER","RX_DONE","TX_DONE"];
I2C1.peripheral.$assign                = "I2C0";
I2C1.peripheral.sdaPin.$assign         = "PA28";
I2C1.peripheral.sclPin.$assign         = "PA31";
I2C1.sdaPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric0";
I2C1.sdaPinConfig.hideOutputInversion  = scripting.forceWrite(false);
I2C1.sdaPinConfig.onlyInternalResistor = scripting.forceWrite(false);
I2C1.sdaPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
I2C1.sclPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric2";
I2C1.sclPinConfig.hideOutputInversion  = scripting.forceWrite(false);
I2C1.sclPinConfig.onlyInternalResistor = scripting.forceWrite(false);
I2C1.sclPinConfig.passedPeripheralType = scripting.forceWrite("Digital");

PWM1.$name                              = "PWM_LED";
PWM1.clockDivider                       = 8;
PWM1.ccIndex                            = [0];
PWM1.timerStartTimer                    = true;
PWM1.peripheral.$assign                 = "TIMG12";
PWM1.peripheral.ccp0Pin.$assign         = "PA14";
PWM1.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC0";
PWM1.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric1";

PWM2.$name                              = "PWM_MOTOR1";
PWM2.ccIndex                            = [0];
PWM2.clockPrescale                      = 32;
PWM2.timerCount                         = 200;
PWM2.peripheral.$assign                 = "TIMG0";
PWM2.peripheral.ccp0Pin.$assign         = "PB10";
PWM2.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC1";
PWM2.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric6";
PWM2.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM2.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM2.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM2.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");

PWM3.$name                              = "PWM_MOTOR2";
PWM3.ccIndex                            = [1];
PWM3.clockPrescale                      = 32;
PWM3.timerCount                         = 200;
PWM3.peripheral.$assign                 = "TIMG6";
PWM3.peripheral.ccp1Pin.$assign         = "PB7";
PWM3.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric9";
PWM3.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM3.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM3.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM3.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM3.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC2";

SYSCTL.forceDefaultClkConfig = true;

SYSTICK.periodEnable      = true;
SYSTICK.period            = 32000;
SYSTICK.interruptEnable   = true;
SYSTICK.interruptPriority = "0";
SYSTICK.systickEnable     = true;

UART1.$name                            = "UART_WIT";
UART1.targetBaudRate                   = 115200;
UART1.direction                        = "RX";
UART1.enableFIFO                       = true;
UART1.rxTimeoutValue                   = 1;
UART1.enabledInterrupts                = ["RX_TIMEOUT_ERROR"];
UART1.enabledDMARXTriggers             = "DL_UART_DMA_INTERRUPT_RX";
UART1.rxPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric4";
UART1.rxPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART1.rxPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART1.rxPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART1.DMA_CHANNEL_RX.$name             = "DMA_CH0";
UART1.DMA_CHANNEL_RX.addressMode       = "f2b";
UART1.DMA_CHANNEL_RX.srcLength         = "BYTE";
UART1.DMA_CHANNEL_RX.dstLength         = "BYTE";

UART2.$name                    = "UART_0";
UART2.targetBaudRate           = 115200;
UART2.peripheral.$assign       = "UART0";
UART2.peripheral.rxPin.$assign = "PA11";
UART2.peripheral.txPin.$assign = "PA10";
UART2.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric3";
UART2.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric5";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
Board.peripheral.$suggestSolution                = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution       = "PA20";
Board.peripheral.swdioPin.$suggestSolution       = "PA19";
SYSCTL.peripheral.$suggestSolution               = "SYSCTL";
UART1.peripheral.$suggestSolution                = "UART1";
UART1.peripheral.rxPin.$suggestSolution          = "PA18";
UART1.DMA_CHANNEL_RX.peripheral.$suggestSolution = "DMA_CH0";
