#include "ti_msp_dl_config.h"
#include "Motor.h"
#include "Key.h"

// 小车控制模式
typedef enum {
    CAR_MODE_STOP = 0,      // 停止模式
    CAR_MODE_LINE_FOLLOW,   // 寻迹模式
    CAR_MODE_MANUAL         // 手动模式
} Car_Mode_t;

static Car_Mode_t current_mode = CAR_MODE_STOP;
static uint8_t manual_direction = 0; // 手动模式方向: 0=停止, 1=前进, 2=左转, 3=右转

// 按键事件处理函数
void Key_EventHandler(Key_Index_t key_index, Key_State_t key_state)
{
    if (key_state == KEY_PRESSED) {
        switch (key_index) {
            case KEY3: // KEY3: 模式切换
                switch (current_mode) {
                    case CAR_MODE_STOP:
                        current_mode = CAR_MODE_LINE_FOLLOW;
                        // 可以在这里添加OLED显示或LED指示
                        break;
                    case CAR_MODE_LINE_FOLLOW:
                        current_mode = CAR_MODE_MANUAL;
                        manual_direction = 0; // 手动模式默认停止
                        Motor_Stop();
                        break;
                    case CAR_MODE_MANUAL:
                        current_mode = CAR_MODE_STOP;
                        Motor_Stop();
                        break;
                }
                break;
                
            case KEY4: // KEY4: 功能键
                if (current_mode == CAR_MODE_MANUAL) {
                    // 手动模式下，KEY4控制方向
                    manual_direction++;
                    if (manual_direction > 3) manual_direction = 0;
                    
                    switch (manual_direction) {
                        case 0: // 停止
                            Motor_Stop();
                            break;
                        case 1: // 前进
                            Motor_Move(800, 0);
                            break;
                        case 2: // 左转
                            Motor_Move(600, -1);
                            break;
                        case 3: // 右转
                            Motor_Move(600, 1);
                            break;
                    }
                } else if (current_mode == CAR_MODE_LINE_FOLLOW) {
                    // 寻迹模式下，KEY4可以用作紧急停止
                    current_mode = CAR_MODE_STOP;
                    Motor_Stop();
                }
                break;
        }
    } else if (key_state == KEY_HOLD) {
        // 长按处理
        if (key_index == KEY3) {
            // KEY3长按：紧急停止
            current_mode = CAR_MODE_STOP;
            Motor_Stop();
        }
    }
}

// 按键应用初始化
void KeyApp_Init(void)
{
    Key_Init();
    Key_RegisterCallback(Key_EventHandler);
    current_mode = CAR_MODE_STOP;
    manual_direction = 0;
}

// 获取当前模式
Car_Mode_t KeyApp_GetMode(void)
{
    return current_mode;
}

// 设置模式
void KeyApp_SetMode(Car_Mode_t mode)
{
    current_mode = mode;
    if (mode == CAR_MODE_STOP) {
        Motor_Stop();
    }
}

// 按键应用处理函数(在Motor_Proc中调用)
void KeyApp_Process(void)
{
    // 根据当前模式执行相应操作
    switch (current_mode) {
        case CAR_MODE_STOP:
            // 停止模式：什么都不做，电机已停止
            break;
            
        case CAR_MODE_LINE_FOLLOW:
            // 寻迹模式：执行寻迹算法
            Motor_LineFollowing();
            break;
            
        case CAR_MODE_MANUAL:
            // 手动模式：保持当前设置的方向和速度
            // 电机控制已在按键事件中处理
            break;
    }
}
