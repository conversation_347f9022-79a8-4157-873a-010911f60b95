#ifndef __KEY_H__
#define __KEY_H__

#include "ti_msp_dl_config.h"

// 按键状态定义
typedef enum {
    KEY_RELEASED = 0,  // 按键释放
    KEY_PRESSED = 1,   // 按键按下
    KEY_HOLD = 2       // 按键长按
} Key_State_t;

// 按键编号定义
typedef enum {
    KEY3 = 0,
    KEY4 = 1,
    KEY_NUM = 2
} Key_Index_t;

// 按键事件回调函数类型
typedef void (*Key_Callback_t)(Key_Index_t key_index, Key_State_t key_state);

// 按键初始化
void Key_Init(void);

// 按键扫描(需要在定时器中调用)
void Key_Scan(void);

// 读取按键状态
Key_State_t Key_GetState(Key_Index_t key_index);

// 检查按键是否按下
uint8_t Key_IsPressed(Key_Index_t key_index);

// 检查按键是否刚按下(单次触发)
uint8_t Key_IsPressedOnce(Key_Index_t key_index);

// 注册按键事件回调函数
void Key_RegisterCallback(Key_Callback_t callback);

// 清除按键状态
void Key_ClearState(Key_Index_t key_index);

#endif
